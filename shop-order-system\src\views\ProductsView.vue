<template>
  <div class="products-view">
    <div class="page-header">
      <h2>商品管理</h2>
      <div class="header-actions">
        <el-button type="warning" @click="showLowStockDialog = true">
          <el-icon><Warning /></el-icon>
          库存预警
        </el-button>
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增商品
        </el-button>
      </div>
    </div>

    <el-card>
      <div class="search-bar">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchQuery"
              placeholder="搜索商品名称或类别"
              prefix-icon="Search"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-select v-model="statusFilter" placeholder="筛选状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="上架" value="true" />
              <el-option label="下架" value="false" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="categoryFilter" placeholder="筛选类别" clearable>
              <el-option label="全部" value="" />
              <el-option
                v-for="category in availableCategories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="stockFilter" placeholder="库存状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="正常" value="normal" />
              <el-option label="低库存" value="low_stock" />
              <el-option label="缺货" value="out_of_stock" />
              <el-option label="超库存" value="overstock" />
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" @click="refreshProducts">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-col>
        </el-row>
      </div>

      <el-table
        :data="filteredProducts"
        style="width: 100%"
        v-loading="productsStore.loading"
        height="600"
      >
        <el-table-column label="图片" width="80">
          <template #default="scope">
            <el-avatar
              :size="50"
              :src="scope.row.image_url"
              icon="Picture"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="商品名称" />
        <el-table-column prop="category" label="类别" />
        <el-table-column prop="price" label="价格" width="100">
          <template #default="scope">
            ¥{{ scope.row.price }}
          </template>
        </el-table-column>
        <el-table-column label="库存" width="120">
          <template #default="scope">
            <div class="stock-info">
              <span :class="getStockClass(scope.row)">
                {{ scope.row.stock_quantity || 0 }}{{ scope.row.stock_unit || '件' }}
              </span>
              <el-tag
                v-if="scope.row.is_low_stock"
                type="warning"
                size="small"
                style="margin-left: 5px;"
              >
                低库存
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="is_listed" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.is_listed"
              active-text="上架"
              inactive-text="下架"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button
              type="success"
              size="small"
              @click="manageStock(scope.row)"
            >
              库存
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="editProduct(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteProduct(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页和加载更多 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>共 {{ productsStore.total }} 条记录，当前显示 {{ productsStore.products.length }} 条</span>
        </div>

        <div class="pagination-actions">
          <el-button
            v-if="productsStore.hasMore"
            type="primary"
            @click="loadMore"
            :loading="productsStore.loading"
          >
            加载更多
          </el-button>

          <el-pagination
            v-model:current-page="productsStore.currentPage"
            :page-size="productsStore.pageSize"
            :total="productsStore.total"
            layout="prev, pager, next, jumper"
            @current-change="handlePageChange"
            style="margin-left: 20px;"
          />
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑商品对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingProduct ? '编辑商品' : '新增商品'"
      width="600px"
    >
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="100px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="productForm.name" />
        </el-form-item>
        
        <el-form-item label="类别" prop="category">
          <el-input v-model="productForm.category" />
        </el-form-item>
        
        <el-form-item label="价格" prop="price">
          <el-input-number
            v-model="productForm.price"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="productForm.is_listed"
            active-text="上架"
            inactive-text="下架"
          />
        </el-form-item>

        <!-- 库存管理 -->
        <el-divider content-position="left">库存管理</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="当前库存" prop="stock_quantity">
              <el-input-number
                v-model="productForm.stock_quantity"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低库存" prop="min_stock_level">
              <el-input-number
                v-model="productForm.min_stock_level"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最高库存" prop="max_stock_level">
              <el-input-number
                v-model="productForm.max_stock_level"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="库存单位" prop="stock_unit">
          <el-input v-model="productForm.stock_unit" placeholder="如：件、个、盒等" style="width: 200px;" />
        </el-form-item>
        
        <el-form-item label="商品图片">
          <ImageUploader
            v-model="productForm.image_url"
            :product-id="editingProduct?.id"
            @upload-success="handleImageUploadSuccess"
            @delete-success="handleImageDeleteSuccess"
          />
        </el-form-item>

        <!-- 自定义属性 -->
        <el-form-item label="自定义属性">
          <div class="custom-attributes">
            <div v-for="(attr, attrIndex) in customAttributes" :key="attrIndex" class="attribute-item">
              <el-input
                v-model="attr.key"
                placeholder="属性名称（如：规格、口味）"
                style="width: 150px; margin-right: 10px;"
              />
              <div class="attribute-values">
                <el-tag
                  v-for="(value, valueIndex) in attr.values"
                  :key="valueIndex"
                  closable
                  @close="removeAttributeValue(attrIndex, valueIndex)"
                  style="margin-right: 5px; margin-bottom: 5px;"
                >
                  <el-input
                    v-model="attr.values[valueIndex]"
                    size="small"
                    style="width: 80px;"
                    placeholder="值"
                  />
                </el-tag>
                <el-button
                  size="small"
                  type="primary"
                  plain
                  @click="addAttributeValue(attrIndex)"
                >
                  + 添加值
                </el-button>
              </div>
              <el-button
                type="danger"
                size="small"
                plain
                @click="removeCustomAttribute(attrIndex)"
                style="margin-left: 10px;"
              >
                删除属性
              </el-button>
            </div>
            <el-button type="primary" plain @click="addCustomAttribute">
              + 添加自定义属性
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveProduct">保存</el-button>
      </template>
    </el-dialog>

    <!-- 库存管理对话框 -->
    <el-dialog
      v-model="showStockDialog"
      title="库存管理"
      width="500px"
    >
      <div v-if="currentProduct">
        <div class="stock-info-header">
          <h4>{{ currentProduct.name }}</h4>
          <el-tag :type="getStockTagType(currentProduct)">
            {{ getStockStatusText(currentProduct) }}
          </el-tag>
        </div>

        <el-descriptions :column="2" border style="margin: 20px 0;">
          <el-descriptions-item label="当前库存">
            {{ currentProduct.stock_quantity || 0 }}{{ currentProduct.stock_unit || '件' }}
          </el-descriptions-item>
          <el-descriptions-item label="最低库存">
            {{ currentProduct.min_stock_level || 0 }}{{ currentProduct.stock_unit || '件' }}
          </el-descriptions-item>
          <el-descriptions-item label="最高库存">
            {{ currentProduct.max_stock_level || 0 }}{{ currentProduct.stock_unit || '件' }}
          </el-descriptions-item>
          <el-descriptions-item label="库存单位">
            {{ currentProduct.stock_unit || '件' }}
          </el-descriptions-item>
        </el-descriptions>

        <el-form :model="stockForm" label-width="100px">
          <el-form-item label="操作类型">
            <el-radio-group v-model="stockForm.type">
              <el-radio value="in">入库</el-radio>
              <el-radio value="out">出库</el-radio>
              <el-radio value="set">设置</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="数量">
            <el-input-number
              v-model="stockForm.quantity"
              :min="1"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="stockForm.reason"
              type="textarea"
              placeholder="请输入操作原因（可选）"
              :rows="3"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showStockDialog = false">取消</el-button>
        <el-button type="primary" @click="updateStock" :loading="stockUpdating">
          确认更新
        </el-button>
      </template>
    </el-dialog>

    <!-- 低库存预警对话框 -->
    <el-dialog
      v-model="showLowStockDialog"
      title="库存预警"
      width="800px"
    >
      <div v-loading="lowStockLoading">
        <div v-if="lowStockProducts.length === 0" class="empty-state">
          <el-empty description="暂无低库存商品" />
        </div>
        <div v-else>
          <el-alert
            :title="`发现 ${lowStockProducts.length} 个商品库存不足`"
            type="warning"
            :closable="false"
            style="margin-bottom: 20px;"
          />

          <el-table :data="lowStockProducts" style="width: 100%">
            <el-table-column prop="name" label="商品名称" />
            <el-table-column prop="category" label="类别" width="100" />
            <el-table-column label="当前库存" width="120">
              <template #default="scope">
                <span :class="getStockClass(scope.row)">
                  {{ scope.row.stock_quantity || 0 }}{{ scope.row.stock_unit || '件' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="最低库存" width="120">
              <template #default="scope">
                {{ scope.row.min_stock_level || 0 }}{{ scope.row.stock_unit || '件' }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStockTagType(scope.row)">
                  {{ getStockStatusText(scope.row) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="manageStockFromAlert(scope.row)"
                >
                  补货
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="showLowStockDialog = false">关闭</el-button>
        <el-button type="primary" @click="refreshLowStock">刷新</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Warning, Refresh } from '@element-plus/icons-vue'
import { useProductsStore } from '../stores/products'
import { productAPI } from '../lib/api'
import ImageUploader from '../components/ImageUploader.vue'

const productsStore = useProductsStore()

const showAddDialog = ref(false)
const editingProduct = ref(null)
const productFormRef = ref()
const searchQuery = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')
const stockFilter = ref('')


// 库存管理相关状态
const showStockDialog = ref(false)
const showLowStockDialog = ref(false)
const currentProduct = ref(null)
const stockUpdating = ref(false)
const lowStockLoading = ref(false)
const lowStockProducts = ref([])

const productForm = reactive({
  name: '',
  category: '',
  price: 0,
  is_listed: true,
  image_url: '',
  custom_attributes: {},
  stock_quantity: 0,
  min_stock_level: 10,
  max_stock_level: 1000,
  stock_unit: '件'
})

// 库存操作表单
const stockForm = reactive({
  type: 'in', // in: 入库, out: 出库, set: 设置
  quantity: 1,
  reason: ''
})

const productRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请输入商品类别', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能为负数', trigger: 'blur' }
  ]
}

// 自定义属性管理
const customAttributes = ref([])

const addCustomAttribute = () => {
  customAttributes.value.push({ key: '', values: [''] })
}

const removeCustomAttribute = (index) => {
  customAttributes.value.splice(index, 1)
}

const addAttributeValue = (attrIndex) => {
  customAttributes.value[attrIndex].values.push('')
}

const removeAttributeValue = (attrIndex, valueIndex) => {
  customAttributes.value[attrIndex].values.splice(valueIndex, 1)
}

const filteredProducts = computed(() => {
  let products = productsStore.searchProducts(searchQuery.value, {
    category: categoryFilter.value,
    status: statusFilter.value === '' ? undefined : statusFilter.value === 'true'
  })

  // 库存状态筛选
  if (stockFilter.value) {
    products = products.filter(product => {
      const stockStatus = getStockStatus(product)
      return stockStatus === stockFilter.value
    })
  }

  return products
})

const availableCategories = computed(() => {
  return productsStore.productCategories
})

const handleStatusChange = async (product) => {
  const result = await productsStore.toggleProductStatus(product.id, product.is_listed)
  if (!result.success) {
    // 回滚状态
    product.is_listed = !product.is_listed
  }
}

// 库存相关方法
const getStockStatus = (product) => {
  const quantity = product.stock_quantity || 0
  const minLevel = product.min_stock_level || 0
  const maxLevel = product.max_stock_level || 1000

  if (quantity <= 0) return 'out_of_stock'
  if (quantity <= minLevel) return 'low_stock'
  if (quantity >= maxLevel) return 'overstock'
  return 'normal'
}

const getStockClass = (product) => {
  const status = getStockStatus(product)
  return {
    'stock-normal': status === 'normal',
    'stock-low': status === 'low_stock',
    'stock-out': status === 'out_of_stock',
    'stock-over': status === 'overstock'
  }
}

const getStockTagType = (product) => {
  const status = getStockStatus(product)
  const typeMap = {
    'normal': 'success',
    'low_stock': 'warning',
    'out_of_stock': 'danger',
    'overstock': 'info'
  }
  return typeMap[status] || 'info'
}

const getStockStatusText = (product) => {
  const status = getStockStatus(product)
  const textMap = {
    'normal': '正常',
    'low_stock': '低库存',
    'out_of_stock': '缺货',
    'overstock': '超库存'
  }
  return textMap[status] || '未知'
}

const manageStock = (product) => {
  currentProduct.value = product
  stockForm.type = 'in'
  stockForm.quantity = 1
  stockForm.reason = ''
  showStockDialog.value = true
}

const updateStock = async () => {
  if (!currentProduct.value) return

  try {
    stockUpdating.value = true

    const result = await productAPI.updateStock(currentProduct.value.id, {
      type: stockForm.type,
      quantity: stockForm.quantity,
      reason: stockForm.reason
    })

    if (result.success) {
      ElMessage.success('库存更新成功')
      showStockDialog.value = false

      // 更新本地数据
      const index = productsStore.products.findIndex(p => p.id === currentProduct.value.id)
      if (index !== -1) {
        Object.assign(productsStore.products[index], result.data)
      }

      // 刷新商品列表
      await productsStore.fetchProducts()
    }
  } catch (error) {
    console.error('更新库存失败:', error)
    ElMessage.error('更新库存失败')
  } finally {
    stockUpdating.value = false
  }
}

const refreshProducts = async () => {
  await productsStore.refreshProducts()
  ElMessage.success('商品列表已刷新')
}

// 分页相关方法
const loadMore = async () => {
  try {
    const result = await productsStore.loadMoreProducts()
    if (result.success) {
      ElMessage.success(`加载了 ${result.data.length} 条新数据`)
    }
  } catch (error) {
    console.error('加载更多失败:', error)
    ElMessage.error('加载更多失败')
  }
}

const handlePageChange = async (page) => {
  productsStore.currentPage = page
  productsStore.resetPagination()
  await productsStore.fetchProducts()
}

const fetchLowStockProducts = async () => {
  try {
    lowStockLoading.value = true
    const result = await productAPI.getLowStockProducts()

    if (result.success) {
      lowStockProducts.value = result.data || []
    }
  } catch (error) {
    console.error('获取低库存商品失败:', error)
    ElMessage.error('获取低库存商品失败')
  } finally {
    lowStockLoading.value = false
  }
}

const refreshLowStock = async () => {
  await fetchLowStockProducts()
}

const manageStockFromAlert = (product) => {
  showLowStockDialog.value = false
  manageStock(product)
}

const editProduct = (product) => {
  editingProduct.value = product
  Object.assign(productForm, {
    name: product.name,
    category: product.category,
    price: product.price,
    is_listed: product.is_listed,
    image_url: product.image_url,
    custom_attributes: product.custom_attributes || {}
  })

  // 转换自定义属性为编辑格式
  customAttributes.value = Object.entries(product.custom_attributes || {}).map(([key, values]) => ({
    key,
    values: Array.isArray(values) ? values : [values]
  }))

  showAddDialog.value = true
}

const deleteProduct = async (product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品"${product.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await productsStore.deleteProduct(product.id)
  } catch (error) {
    if (error.message) {
      console.error('删除商品失败:', error)
    }
    // 用户取消删除或删除失败
  }
}

const saveProduct = async () => {
  if (!productFormRef.value) return

  await productFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 处理自定义属性
        const attributes = {}
        customAttributes.value.forEach(attr => {
          if (attr.key.trim()) {
            attributes[attr.key.trim()] = attr.values.filter(v => v.trim())
          }
        })

        const productData = {
          ...productForm,
          custom_attributes: attributes
        }

        let result
        if (editingProduct.value) {
          // 编辑模式
          result = await productsStore.updateProduct(editingProduct.value.id, productData)
        } else {
          // 新增模式
          result = await productsStore.createProduct(productData)
        }

        if (result.success) {
          showAddDialog.value = false
          resetForm()
        }
      } catch (error) {
        console.error('保存商品失败:', error)
      }
    }
  })
}

const resetForm = () => {
  editingProduct.value = null
  Object.assign(productForm, {
    name: '',
    category: '',
    price: 0,
    is_listed: true,
    image_url: '',
    custom_attributes: {}
  })
  customAttributes.value = []
}

// 图片上传成功处理
const handleImageUploadSuccess = (uploadResult) => {
  console.log('图片上传成功:', uploadResult)
}

// 图片删除成功处理
const handleImageDeleteSuccess = () => {
  console.log('图片删除成功')
}

// 组件挂载时获取数据
onMounted(() => {
  productsStore.fetchProducts()

  // 监听低库存预警对话框打开
  const unwatchLowStock = watch(() => showLowStockDialog.value, (newVal) => {
    if (newVal) {
      fetchLowStockProducts()
    }
  })
})
</script>

<style scoped>
.products-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.page-header h2 {
  margin: 0;
}

.search-bar {
  margin-bottom: 20px;
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.custom-attributes {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.attribute-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.attribute-values {
  flex: 1;
  margin: 0 10px;
}

.attribute-values .el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

/* 库存相关样式 */
.stock-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.stock-normal {
  color: #67c23a;
  font-weight: bold;
}

.stock-low {
  color: #e6a23c;
  font-weight: bold;
}

.stock-out {
  color: #f56c6c;
  font-weight: bold;
}

.stock-over {
  color: #409eff;
  font-weight: bold;
}

.stock-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stock-info-header h4 {
  margin: 0;
  font-size: 18px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

/* 分页相关样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 15px 0;
  border-top: 1px solid #ebeef5;
}

.pagination-info {
  color: #606266;
  font-size: 14px;
}

.pagination-actions {
  display: flex;
  align-items: center;
}
</style>


