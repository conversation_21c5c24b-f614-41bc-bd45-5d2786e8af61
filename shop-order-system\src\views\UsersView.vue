<template>
  <div class="users-view">
    <div class="page-header">
      <h2>用户管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新增用户
      </el-button>
    </div>

    <el-card>
      <div class="search-bar">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchQuery"
              placeholder="搜索用户邮箱"
              prefix-icon="Search"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-select v-model="roleFilter" placeholder="筛选角色" clearable>
              <el-option label="全部" value="" />
              <el-option label="超级管理员" value="super_admin" />
              <el-option label="普通员工" value="staff" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="statusFilter" placeholder="筛选状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="活跃" value="true" />
              <el-option label="禁用" value="false" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <el-table :data="filteredUsers" style="width: 100%" v-loading="loading">
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role" label="角色">
          <template #default="scope">
            <el-tag :type="getRoleType(scope.row.role)">
              {{ getRoleText(scope.row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态">
          <template #default="scope">
            <el-switch
              v-model="scope.row.is_active"
              active-text="活跃"
              inactive-text="禁用"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="editUser(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="resetPassword(scope.row)"
            >
              重置密码
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteUser(scope.row)"
              :disabled="scope.row.id === authStore.user?.id"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑用户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingUser ? '编辑用户' : '新增用户'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="userForm.email"
            :disabled="!!editingUser"
            placeholder="请输入邮箱地址"
          />
        </el-form-item>

        <el-form-item label="密码" prop="password" v-if="!editingUser">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入初始密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" style="width: 100%">
            <el-option label="普通员工" value="staff" />
            <el-option label="超级管理员" value="super_admin" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-switch
            v-model="userForm.is_active"
            active-text="活跃"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import { supabase } from '../lib/supabase'

const authStore = useAuthStore()

// 状态
const users = ref([])
const loading = ref(false)
const saving = ref(false)
const showAddDialog = ref(false)
const editingUser = ref(null)
const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')

// 表单数据
const userForm = reactive({
  email: '',
  password: '',
  role: 'staff',
  is_active: true
})

// 表单验证规则
const userRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const userFormRef = ref()

// 计算属性
const filteredUsers = computed(() => {
  return users.value.filter(user => {
    const matchesSearch = !searchQuery.value ||
      user.email.toLowerCase().includes(searchQuery.value.toLowerCase())

    const matchesRole = !roleFilter.value || user.role === roleFilter.value

    const matchesStatus = !statusFilter.value ||
      user.is_active.toString() === statusFilter.value

    return matchesSearch && matchesRole && matchesStatus
  })
})

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true

    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    users.value = data || []
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取角色类型
const getRoleType = (role) => {
  return role === 'super_admin' ? 'danger' : 'primary'
}

// 获取角色文本
const getRoleText = (role) => {
  return role === 'super_admin' ? '超级管理员' : '普通员工'
}

// 处理状态变更
const handleStatusChange = async (user) => {
  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({ is_active: user.is_active })
      .eq('id', user.id)

    if (error) {
      throw error
    }

    ElMessage.success(`用户${user.email}已${user.is_active ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('更新用户状态失败:', error)
    ElMessage.error('更新用户状态失败')
    // 回滚状态
    user.is_active = !user.is_active
  }
}

// 编辑用户
const editUser = (user) => {
  editingUser.value = user
  Object.assign(userForm, {
    email: user.email,
    password: '',
    role: user.role,
    is_active: user.is_active
  })
  showAddDialog.value = true
}

// 保存用户
const saveUser = async () => {
  if (!userFormRef.value) return

  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saving.value = true

        if (editingUser.value) {
          // 编辑模式 - 只更新档案信息
          const { error } = await supabase
            .from('user_profiles')
            .update({
              role: userForm.role,
              is_active: userForm.is_active
            })
            .eq('id', editingUser.value.id)

          if (error) {
            throw error
          }

          ElMessage.success('用户信息更新成功')
        } else {
          // 新增模式 - 创建新用户
          const result = await authStore.signUp(userForm.email, userForm.password, userForm.role)

          if (!result.success) {
            return
          }
        }

        showAddDialog.value = false
        resetForm()
        await fetchUsers()

      } catch (error) {
        console.error('保存用户失败:', error)
        ElMessage.error('保存用户失败')
      } finally {
        saving.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  editingUser.value = null
  Object.assign(userForm, {
    email: '',
    password: '',
    role: 'staff',
    is_active: true
  })
}

// 重置密码
const resetPassword = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户"${user.email}"的密码吗？重置邮件将发送到该邮箱。`,
      '确认重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const result = await authStore.resetPassword(user.email)

    if (result.success) {
      ElMessage.success('密码重置邮件已发送')
    }
  } catch {
    // 用户取消操作
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${user.email}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 注意：Supabase Auth用户删除需要管理员权限
    // 这里只是禁用用户档案
    const { error } = await supabase
      .from('user_profiles')
      .update({ is_active: false })
      .eq('id', user.id)

    if (error) {
      throw error
    }

    ElMessage.success('用户已禁用')
    await fetchUsers()

  } catch (error) {
    if (error.message) {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.users-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.search-bar {
  margin-bottom: 20px;
}
</style>
