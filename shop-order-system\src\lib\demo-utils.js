import { ElMessage } from 'element-plus'
import { isSupabaseConfigured } from './supabase'

// 检查是否为演示模式，如果是则显示提示并返回false
export const checkDemoMode = (action = '保存数据') => {
  if (!isSupabaseConfigured) {
    ElMessage.warning(`演示模式下无法${action}，请配置Supabase以启用完整功能`)
    return true // 是演示模式
  }
  return false // 不是演示模式
}

// 演示模式下的通用错误返回
export const demoModeError = (action = '操作') => ({
  success: false,
  error: new Error(`演示模式下无法${action}`)
})
