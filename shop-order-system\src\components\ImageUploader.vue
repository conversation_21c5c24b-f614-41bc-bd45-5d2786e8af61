<template>
  <div class="image-uploader">
    <el-upload
      ref="uploadRef"
      class="image-upload"
      :http-request="handleUpload"
      :before-upload="beforeUpload"
      :show-file-list="false"
      :disabled="disabled"
      accept="image/*"
      drag
    >
      <div v-if="imageUrl" class="image-preview">
        <img :src="imageUrl" :alt="alt" />
        <div class="image-overlay">
          <div class="image-actions">
            <el-button
              type="primary"
              :icon="Edit"
              circle
              size="small"
              @click.stop="handleReplace"
            />
            <el-button
              type="info"
              :icon="FolderOpened"
              circle
              size="small"
              @click.stop="openFileManager"
            />
            <el-button
              type="danger"
              :icon="Delete"
              circle
              size="small"
              @click.stop="handleDelete"
            />
          </div>
        </div>
      </div>
      
      <div v-else class="upload-placeholder" v-loading="uploading">
        <el-icon class="upload-icon"><Plus /></el-icon>
        <div class="upload-text">
          <p>点击或拖拽图片到此处上传</p>
          <p class="upload-tip">支持 JPG、PNG、GIF、WebP 格式，大小不超过 {{ maxSizeMB }}MB</p>
          <div class="upload-actions">
            <el-button type="text" @click.stop="openFileManager" :icon="FolderOpened">
              从文件库选择
            </el-button>
          </div>
        </div>
      </div>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="showPreview"
      title="图片预览"
      width="60%"
      center
    >
      <div class="preview-container">
        <img :src="imageUrl" :alt="alt" style="max-width: 100%; max-height: 70vh;" />
      </div>
      <template #footer>
        <el-button @click="showPreview = false">关闭</el-button>
        <el-button type="danger" @click="handleDelete">删除图片</el-button>
      </template>
    </el-dialog>

    <!-- 文件管理器 -->
    <FileManager
      v-model="showFileManager"
      @file-selected="handleFileSelected"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, FolderOpened } from '@element-plus/icons-vue'
import { productAPI } from '../lib/api'
import FileManager from './FileManager.vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  maxSizeMB: {
    type: Number,
    default: 5
  },
  alt: {
    type: String,
    default: '商品图片'
  },
  productId: {
    type: [String, Number],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error', 'delete-success'])

// 状态
const uploading = ref(false)
const showPreview = ref(false)
const showFileManager = ref(false)
const uploadRef = ref()

// 计算属性
const imageUrl = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const beforeUpload = (file) => {
  // 验证文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 验证文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSizeMB
  if (!isLtMaxSize) {
    ElMessage.error(`图片大小不能超过 ${props.maxSizeMB}MB!`)
    return false
  }

  // 验证图片格式
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  if (!validTypes.includes(file.type)) {
    ElMessage.error('只支持 JPG、PNG、GIF、WebP 格式的图片!')
    return false
  }

  return true
}

const handleUpload = async (options) => {
  const { file } = options

  if (!beforeUpload(file)) {
    return
  }

  try {
    uploading.value = true

    const formData = new FormData()
    formData.append('file', file)
    if (props.productId) {
      formData.append('product_id', props.productId)
    }

    const result = await productAPI.uploadImage(formData)

    if (result.success) {
      imageUrl.value = result.url
      emit('upload-success', {
        url: result.url,
        filename: result.filename,
        size: result.size
      })
      ElMessage.success('图片上传成功')
    } else {
      throw new Error(result.error || '上传失败')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    ElMessage.error('上传图片失败')
    emit('upload-error', error)
  } finally {
    uploading.value = false
  }
}

const handleReplace = () => {
  if (uploadRef.value) {
    uploadRef.value.$el.querySelector('input[type="file"]').click()
  }
}

const handleDelete = async () => {
  if (!imageUrl.value) return

  try {
    await ElMessageBox.confirm(
      '确定要删除这张图片吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 从URL中提取文件路径信息
    const url = new URL(imageUrl.value)
    const pathParts = url.pathname.split('/uploads/')[1]
    const filename = pathParts.split('/').pop()

    if (pathParts && filename) {
      const result = await productAPI.deleteImage(filename, pathParts)
      if (result.success) {
        imageUrl.value = ''
        emit('delete-success')
        ElMessage.success('图片删除成功')
      }
    } else {
      // 如果无法解析路径，直接清空URL
      imageUrl.value = ''
      emit('delete-success')
      ElMessage.success('图片已移除')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除图片失败:', error)
      ElMessage.error('删除图片失败')
    }
  }
}

const handlePreview = () => {
  if (imageUrl.value) {
    showPreview.value = true
  }
}

const openFileManager = () => {
  showFileManager.value = true
}

const handleFileSelected = (file) => {
  imageUrl.value = file.url
  emit('upload-success', {
    url: file.url,
    filename: file.filename,
    size: file.size
  })
  ElMessage.success('图片选择成功')
}

// 暴露方法给父组件
defineExpose({
  handlePreview,
  handleDelete,
  handleReplace,
  openFileManager
})
</script>

<style scoped>
.image-uploader {
  width: 100%;
}

.image-upload {
  width: 100%;
}

.image-preview {
  position: relative;
  width: 178px;
  height: 178px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.upload-placeholder {
  width: 178px;
  height: 178px;
  border: 2px dashed var(--el-border-color);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: var(--el-color-primary);
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-text p {
  margin: 0;
  color: #606266;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.upload-actions {
  margin-top: 8px;
}

.preview-container {
  text-align: center;
}
</style>

<style>
.image-upload .el-upload {
  border: none;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.image-upload .el-upload:hover {
  border-color: var(--el-color-primary);
}

.image-upload .el-upload-dragger {
  padding: 0;
  width: 178px;
  height: 178px;
  border: none;
  border-radius: 6px;
}

.image-upload .el-upload-dragger:hover {
  border-color: var(--el-color-primary);
}
</style>
