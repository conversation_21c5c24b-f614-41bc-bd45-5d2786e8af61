package controllers

import (
	"net/http"
	"strconv"

	"shop-order-backend/internal/models"

	"github.com/gin-gonic/gin"
	"xorm.io/xorm"
)

type CustomerController struct {
	db *xorm.Engine
}

func NewCustomerController(db *xorm.Engine) *CustomerController {
	return &CustomerController{db: db}
}

// GetCustomers 获取客户列表
func (cc *CustomerController) GetCustomers(c *gin.Context) {
	// 获取查询参数
	search := c.Query("search")
	pageStr := c.<PERSON>fault<PERSON>y("page", "1")
	limitStr := c.<PERSON>("limit", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 构建查询
	session := cc.db.NewSession()
	defer session.Close()

	// 如果有搜索条件，添加模糊搜索
	if search != "" {
		session = session.Where("name LIKE ? OR contact LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 获取总数
	total, err := session.Count(&models.Customer{})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to count customers",
		})
		return
	}

	// 获取分页数据
	var customers []models.Customer
	err = session.Limit(limit, offset).OrderBy("created_at DESC").Find(&customers)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch customers",
		})
		return
	}

	// 为每个客户添加地址数量和基本地址信息
	for i := range customers {
		// 获取地址数量
		addressCount, _ := cc.db.Where("customer_id = ?", customers[i].ID).Count(&models.Address{})
		customers[i].AddressCount = int(addressCount)

		// 如果不是搜索模式，加载完整地址信息
		if search == "" {
			var addresses []models.Address
			if err := cc.db.Where("customer_id = ?", customers[i].ID).Find(&addresses); err == nil {
				customers[i].Addresses = make([]*models.Address, len(addresses))
				for j := range addresses {
					customers[i].Addresses[j] = &addresses[j]
				}
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": customers,
		"pagination": gin.H{
			"page":       page,
			"limit":      limit,
			"total":      total,
			"totalPages": (total + int64(limit) - 1) / int64(limit),
		},
		"success": true,
	})
}

// GetCustomer 获取单个客户信息
func (cc *CustomerController) GetCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid customer ID",
		})
		return
	}

	var customer models.Customer
	has, err := cc.db.ID(id).Get(&customer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Customer not found",
		})
		return
	}

	// 加载地址信息
	var addresses []models.Address
	if err := cc.db.Where("customer_id = ?", customer.ID).Find(&addresses); err == nil {
		customer.Addresses = make([]*models.Address, len(addresses))
		for i := range addresses {
			customer.Addresses[i] = &addresses[i]
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": customer,
	})
}

// CreateCustomer 创建客户
func (cc *CustomerController) CreateCustomer(c *gin.Context) {
	var req models.CustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	customer := &models.Customer{
		Name:    req.Name,
		Contact: req.Contact,
	}

	if _, err := cc.db.Insert(customer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create customer",
		})
		return
	}

	// 初始化空地址数组
	customer.Addresses = []*models.Address{}

	c.JSON(http.StatusCreated, gin.H{
		"data": customer,
	})
}

// UpdateCustomer 更新客户信息
func (cc *CustomerController) UpdateCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid customer ID",
		})
		return
	}

	var req models.CustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 检查客户是否存在
	var customer models.Customer
	has, err := cc.db.ID(id).Get(&customer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Customer not found",
		})
		return
	}

	// 更新客户信息
	customer.Name = req.Name
	customer.Contact = req.Contact

	if _, err := cc.db.ID(id).Update(&customer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update customer",
		})
		return
	}

	// 加载地址信息
	var addresses []models.Address
	if err := cc.db.Where("customer_id = ?", customer.ID).Find(&addresses); err == nil {
		customer.Addresses = make([]*models.Address, len(addresses))
		for i := range addresses {
			customer.Addresses[i] = &addresses[i]
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": customer,
	})
}

// DeleteCustomer 删除客户
func (cc *CustomerController) DeleteCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid customer ID",
		})
		return
	}

	// 检查客户是否存在
	var customer models.Customer
	has, err := cc.db.ID(id).Get(&customer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Customer not found",
		})
		return
	}

	// 删除客户（地址会因为外键约束自动删除）
	if _, err := cc.db.ID(id).Delete(&models.Customer{}); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete customer",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Customer deleted successfully",
	})
}
