<template>
  <div class="customer-detail-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        <h2 v-if="customer">{{ customer.name }} 的详细信息</h2>
        <h2 v-else>客户详情</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="editCustomer" :icon="Edit">编辑客户</el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="customer" class="customer-content">
      <!-- 客户基本信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="text" @click="editCustomer" :icon="Edit">编辑</el-button>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="客户姓名">
            {{ customer.name }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ customer.contact || '未填写' }}
          </el-descriptions-item>
          <el-descriptions-item label="地址数量">
            <el-tag type="info">{{ customer.addresses?.length || 0 }}个地址</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(customer.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            {{ formatDateTime(customer.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="默认地址">
            <span v-if="defaultAddress">{{ defaultAddress.address_details }}</span>
            <span v-else class="text-muted">未设置默认地址</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 地址管理卡片 -->
      <el-card class="address-card">
        <template #header>
          <div class="card-header">
            <span>地址管理 ({{ customer.addresses?.length || 0 }}个地址)</span>
            <div class="header-actions">
              <el-button type="primary" size="small" @click="showAddAddressDialog = true" :icon="Plus">
                添加地址
              </el-button>
              <el-button
                v-if="customer.addresses?.length > 1"
                size="small"
                @click="manageAllAddresses"
              >
                批量管理
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="customer.addresses && customer.addresses.length > 0">
          <div class="address-grid">
            <div 
              v-for="address in sortedAddresses" 
              :key="address.id"
              class="address-item"
              :class="{ 'is-default': address.is_default }"
            >
              <div class="address-header">
                <div class="address-title">
                  <span class="recipient-name">{{ address.recipient_name }}</span>
                  <el-tag v-if="address.is_default" type="success" size="small">默认</el-tag>
                </div>
                <div class="address-actions">
                  <el-button type="text" size="small" @click="editAddress(address)" :icon="Edit">编辑</el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click="deleteAddress(address)"
                    :disabled="address.is_default"
                    :icon="Delete"
                    class="delete-btn"
                  >
                    删除
                  </el-button>
                  <el-button
                    v-if="!address.is_default"
                    type="text"
                    size="small"
                    @click="setAsDefault(address)"
                  >
                    设为默认
                  </el-button>
                </div>
              </div>
              
              <div class="address-content">
                <div class="address-phone">
                  <el-icon><Phone /></el-icon>
                  {{ address.recipient_phone }}
                </div>
                <div class="address-details">
                  <el-icon><Location /></el-icon>
                  {{ address.address_details }}
                </div>
                <div class="address-meta">
                  <span class="created-time">添加于 {{ formatDate(address.created_at) }}</span>
                  <div class="address-status">
                    <el-tag v-if="address.is_default" type="success" size="small">
                      <el-icon><Star /></el-icon>
                      默认地址
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="empty-addresses">
          <el-empty description="暂无地址信息">
            <el-button type="primary" @click="showAddAddressDialog = true" :icon="Plus">
              添加第一个地址
            </el-button>
          </el-empty>
        </div>
      </el-card>

      <!-- 订单历史卡片 -->
      <el-card class="orders-card">
        <template #header>
          <div class="card-header">
            <span>订单历史</span>
            <el-button type="text" @click="viewAllOrders">查看全部</el-button>
          </div>
        </template>

        <div v-if="recentOrders.length > 0">
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="id" label="订单号" width="100" />
            <el-table-column label="订单金额" width="120">
              <template #default="scope">
                ¥{{ parseFloat(scope.row.total_price || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="订单状态" width="100">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.order_status)">
                  {{ getOrderStatusText(scope.row.order_status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="付款状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.payment_status === 'paid' ? 'success' : 'danger'">
                  {{ scope.row.payment_status === 'paid' ? '已付款' : '未付款' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="下单时间" width="150">
              <template #default="scope">
                {{ formatDateTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="text" size="small" @click="viewOrder(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-else class="empty-orders">
          <el-empty description="暂无订单记录" />
        </div>
      </el-card>
    </div>

    <div v-else class="error-container">
      <el-result
        icon="warning"
        title="客户不存在"
        sub-title="请检查客户ID是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回客户列表</el-button>
        </template>
      </el-result>
    </div>

    <!-- 编辑客户对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑客户信息"
      width="500px"
      @close="resetCustomerForm"
    >
      <el-form
        ref="customerFormRef"
        :model="customerForm"
        :rules="customerRules"
        label-width="100px"
      >
        <el-form-item label="客户姓名" prop="name">
          <el-input v-model="customerForm.name" placeholder="请输入客户姓名" />
        </el-form-item>

        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="customerForm.contact" placeholder="请输入手机号或微信号" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCustomer" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 新增/编辑地址对话框 -->
    <el-dialog
      v-model="showAddAddressDialog"
      :title="editingAddress ? '编辑地址' : '新增地址'"
      width="600px"
      @close="resetAddressForm"
    >
      <el-form
        ref="addressFormRef"
        :model="addressForm"
        :rules="addressRules"
        label-width="100px"
      >
        <el-form-item label="收货人" prop="recipient_name">
          <el-input
            v-model="addressForm.recipient_name"
            placeholder="请输入收货人姓名"
            clearable
          />
          <div class="form-tip">
            <el-button
              type="text"
              size="small"
              @click="fillCustomerInfo"
            >
              使用客户信息
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="联系电话" prop="recipient_phone">
          <el-input
            v-model="addressForm.recipient_phone"
            placeholder="请输入联系电话"
            clearable
          />
        </el-form-item>

        <el-form-item label="详细地址" prop="address_details">
          <el-input
            v-model="addressForm.address_details"
            type="textarea"
            :rows="3"
            placeholder="请输入详细地址，如：省市区街道门牌号"
            show-word-limit
            maxlength="200"
          />
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="addressForm.is_default" />
          <span class="form-tip">设为默认后，新订单将优先使用此地址</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddAddressDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAddress" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 批量管理地址对话框 -->
    <el-dialog
      v-model="showBatchManageDialog"
      title="批量管理地址"
      width="800px"
    >
      <div class="batch-manage-content">
        <div class="batch-actions">
          <el-button @click="selectAllAddresses">全选</el-button>
          <el-button @click="clearSelection">清空选择</el-button>
          <el-button
            type="danger"
            @click="batchDeleteAddresses"
            :disabled="selectedAddresses.length === 0"
          >
            批量删除 ({{ selectedAddresses.length }})
          </el-button>
        </div>

        <el-table
          ref="addressTableRef"
          :data="customer.addresses"
          @selection-change="handleSelectionChange"
          style="margin-top: 16px;"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="recipient_name" label="收货人" width="120" />
          <el-table-column prop="recipient_phone" label="联系电话" width="130" />
          <el-table-column prop="address_details" label="详细地址" min-width="200" />
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.is_default" type="success" size="small">默认</el-tag>
              <span v-else class="text-muted">普通</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="text" size="small" @click="editAddress(scope.row)">编辑</el-button>
              <el-button
                v-if="!scope.row.is_default"
                type="text"
                size="small"
                @click="setAsDefault(scope.row)"
              >
                设为默认
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <el-button @click="showBatchManageDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Edit, Plus, Delete, Phone, Location, Star } from '@element-plus/icons-vue'
import { useCustomersStore } from '../stores/customers'
import { useOrdersStore } from '../stores/orders'

const route = useRoute()
const router = useRouter()
const customersStore = useCustomersStore()
const ordersStore = useOrdersStore()

// 状态
const customer = ref(null)
const loading = ref(true)
const saving = ref(false)
const showEditDialog = ref(false)
const showAddAddressDialog = ref(false)
const showBatchManageDialog = ref(false)
const editingAddress = ref(null)
const recentOrders = ref([])
const selectedAddresses = ref([])
const addressTableRef = ref()

// 表单数据
const customerForm = reactive({
  name: '',
  contact: ''
})

const addressForm = reactive({
  recipient_name: '',
  recipient_phone: '',
  address_details: '',
  is_default: false
})

// 表单验证规则
const customerRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ]
}

const addressRules = {
  recipient_name: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' }
  ],
  recipient_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  address_details: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ]
}

const customerFormRef = ref()
const addressFormRef = ref()

// 计算属性
const defaultAddress = computed(() => {
  return customer.value?.addresses?.find(addr => addr.is_default)
})

const sortedAddresses = computed(() => {
  if (!customer.value?.addresses) return []

  return [...customer.value.addresses].sort((a, b) => {
    // 默认地址优先
    if (a.is_default && !b.is_default) return -1
    if (!a.is_default && b.is_default) return 1

    // 按创建时间倒序
    const dateA = new Date(a.created_at || 0)
    const dateB = new Date(b.created_at || 0)
    return dateB - dateA
  })
})

// 方法
const goBack = () => {
  router.push('/customers')
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const editCustomer = () => {
  if (customer.value) {
    Object.assign(customerForm, {
      name: customer.value.name,
      contact: customer.value.contact
    })
    showEditDialog.value = true
  }
}

const saveCustomer = async () => {
  if (!customerFormRef.value) return

  await customerFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saving.value = true
        const result = await customersStore.updateCustomer(customer.value.id, customerForm)

        if (result.success) {
          // 更新本地客户数据
          Object.assign(customer.value, customerForm)
          showEditDialog.value = false
          ElMessage.success('客户信息更新成功')
        }
      } catch (error) {
        console.error('更新客户失败:', error)
        ElMessage.error('更新客户失败')
      } finally {
        saving.value = false
      }
    }
  })
}

const editAddress = (address) => {
  editingAddress.value = address
  Object.assign(addressForm, {
    recipient_name: address.recipient_name,
    recipient_phone: address.recipient_phone,
    address_details: address.address_details,
    is_default: address.is_default
  })
  showAddAddressDialog.value = true
}

const saveAddress = async () => {
  if (!addressFormRef.value) return

  await addressFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saving.value = true

        let result
        if (editingAddress.value) {
          result = await customersStore.updateAddress(editingAddress.value.id, addressForm)
        } else {
          result = await customersStore.addAddress(customer.value.id, addressForm)
        }

        if (result.success) {
          // 重新加载客户数据
          await loadCustomer()
          showAddAddressDialog.value = false
          resetAddressForm()
          ElMessage.success(editingAddress.value ? '地址更新成功' : '地址添加成功')
        }
      } catch (error) {
        console.error('保存地址失败:', error)
        ElMessage.error('保存地址失败')
      } finally {
        saving.value = false
      }
    }
  })
}

const deleteAddress = async (address) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个地址吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const result = await customersStore.deleteAddress(address.id)
    if (result.success) {
      // 重新加载客户数据
      await loadCustomer()
      ElMessage.success('地址删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除地址失败:', error)
      ElMessage.error('删除地址失败')
    }
  }
}

const setDefaultAddress = async (address) => {
  if (address.is_default) {
    try {
      const result = await customersStore.setDefaultAddress(customer.value.id, address.id)
      if (result.success) {
        // 重新加载客户数据
        await loadCustomer()
        ElMessage.success('默认地址设置成功')
      }
    } catch (error) {
      console.error('设置默认地址失败:', error)
      ElMessage.error('设置默认地址失败')
      // 恢复开关状态
      address.is_default = false
    }
  }
}

const setAsDefault = async (address) => {
  try {
    const result = await customersStore.setDefaultAddress(customer.value.id, address.id)
    if (result.success) {
      // 重新加载客户数据
      await loadCustomer()
      ElMessage.success('默认地址设置成功')
    }
  } catch (error) {
    console.error('设置默认地址失败:', error)
    ElMessage.error('设置默认地址失败')
  }
}

const manageAllAddresses = () => {
  showBatchManageDialog.value = true
  selectedAddresses.value = []
}

const handleSelectionChange = (selection) => {
  selectedAddresses.value = selection
}

const selectAllAddresses = () => {
  if (addressTableRef.value) {
    addressTableRef.value.toggleAllSelection()
  }
}

const clearSelection = () => {
  if (addressTableRef.value) {
    addressTableRef.value.clearSelection()
  }
}

const batchDeleteAddresses = async () => {
  if (selectedAddresses.value.length === 0) {
    ElMessage.warning('请选择要删除的地址')
    return
  }

  // 检查是否包含默认地址
  const hasDefaultAddress = selectedAddresses.value.some(addr => addr.is_default)
  if (hasDefaultAddress) {
    ElMessage.warning('不能删除默认地址，请先设置其他地址为默认地址')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedAddresses.value.length} 个地址吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 批量删除
    const deletePromises = selectedAddresses.value.map(address =>
      customersStore.deleteAddress(address.id)
    )

    await Promise.all(deletePromises)

    // 重新加载客户数据
    await loadCustomer()
    showBatchManageDialog.value = false
    ElMessage.success(`成功删除 ${selectedAddresses.value.length} 个地址`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除地址失败:', error)
      ElMessage.error('批量删除地址失败')
    }
  }
}

const fillCustomerInfo = () => {
  if (customer.value) {
    addressForm.recipient_name = customer.value.name
    addressForm.recipient_phone = customer.value.contact
  }
}

const resetCustomerForm = () => {
  Object.assign(customerForm, {
    name: '',
    contact: ''
  })
}

const resetAddressForm = () => {
  editingAddress.value = null
  Object.assign(addressForm, {
    recipient_name: '',
    recipient_phone: '',
    address_details: '',
    is_default: false
  })
}

const getOrderStatusType = (status) => {
  const typeMap = {
    'pending': 'danger',
    'production': 'info',
    'delivery': 'warning',
    'completed': 'success',
    'cancelled': ''
  }
  return typeMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const textMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return textMap[status] || status
}

const viewOrder = (order) => {
  router.push(`/orders/${order.id}`)
}

const viewAllOrders = () => {
  router.push(`/orders?customer_id=${customer.value.id}`)
}

const loadCustomer = async () => {
  try {
    loading.value = true
    const customerId = route.params.id

    const result = await customersStore.getCustomer(customerId)
    if (result.success) {
      customer.value = result.data

      // 加载最近的订单
      await loadRecentOrders()
    } else {
      customer.value = null
    }
  } catch (error) {
    console.error('加载客户信息失败:', error)
    customer.value = null
  } finally {
    loading.value = false
  }
}

const loadRecentOrders = async () => {
  try {
    const result = await ordersStore.getOrdersByCustomer(customer.value.id, { limit: 5 })
    if (result.success) {
      recentOrders.value = result.data
    }
  } catch (error) {
    console.error('加载订单历史失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadCustomer()
})
</script>

<style scoped>
.customer-detail-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.loading-container,
.error-container {
  padding: 40px;
}

.customer-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card,
.address-card,
.orders-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.address-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.address-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s;
}

.address-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.address-item.is-default {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.address-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recipient-name {
  font-weight: bold;
  color: #303133;
}

.address-actions {
  display: flex;
  gap: 8px;
}

.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  color: #f56c6c;
  background-color: #fef0f0;
}

.address-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.address-phone,
.address-details {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.address-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

.created-time {
  font-size: 12px;
  color: #909399;
}

.empty-addresses,
.empty-orders {
  padding: 40px;
  text-align: center;
}

.text-muted {
  color: #909399;
}

.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.batch-manage-content {
  padding: 0;
}

.batch-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.address-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.text-muted {
  color: #909399;
}
</style>
