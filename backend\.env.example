# 服务器配置
PORT=8080
GIN_MODE=debug

# 数据库配置
DB_TYPE=sqlite  # sqlite 或 mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=shop_order
DB_USER=root
DB_PASSWORD=
DB_CHARSET=utf8mb4

# SQLite 配置（当 DB_TYPE=sqlite 时使用）
SQLITE_PATH=./data/shop_order.db

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE_HOURS=24

# CORS 配置
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_UPLOAD_SIZE=10  # MB
