# 小商店订单管理系统 - 快速开始指南

## 🚀 立即体验（演示模式）

系统已配置为演示模式，您可以立即体验所有功能：

1. **访问地址**: http://localhost:5173/
2. **演示账号**:
   - 管理员：`<EMAIL>` / `demo123`
   - 员工：`<EMAIL>` / `demo123`

## 📋 演示模式功能

在演示模式下，您可以体验：
- ✅ 用户登录和权限管理
- ✅ 商品管理（查看、搜索、筛选）
- ✅ 客户管理（查看、地址管理）
- ✅ 订单管理（查看、详情、状态）
- ✅ 生产日历（订单日程安排）
- ✅ 数据可视化仪表盘
- ✅ 用户管理（仅管理员）

⚠️ **注意**: 演示模式下的数据修改不会保存，刷新页面后会恢复初始状态。

## 🔧 启用完整功能（Supabase配置）

要启用数据持久化和完整功能，请按以下步骤配置Supabase：

### 1. 创建Supabase项目
1. 访问 [Supabase](https://supabase.com)
2. 注册账号并创建新项目
3. 等待项目初始化完成

### 2. 获取项目配置
1. 在项目仪表盘中，点击左侧的 "Settings" → "API"
2. 复制以下信息：
   - **Project URL** (项目URL)
   - **anon public** key (匿名公钥)

### 3. 配置环境变量
1. 在项目根目录找到 `.env` 文件
2. 替换其中的配置：
   ```env
   VITE_SUPABASE_URL=你的项目URL
   VITE_SUPABASE_ANON_KEY=你的匿名公钥
   ```

### 4. 设置数据库
1. 在Supabase控制台中，点击左侧的 "SQL Editor"
2. 按顺序执行以下SQL文件：
   - `database/schema.sql` - 创建表结构
   - `database/rls_policies.sql` - 设置安全策略
   - `database/sample_data.sql` - 插入示例数据（可选）

### 5. 配置存储桶（可选）
如果需要图片上传功能：
1. 在Supabase控制台中，点击左侧的 "Storage"
2. 创建名为 `product-images` 的存储桶
3. 设置为公开访问

### 6. 重启服务器
```bash
# 停止当前服务器 (Ctrl+C)
# 重新启动
npm run dev
```

## 👤 创建管理员账户

数据库设置完成后：
1. 在Supabase控制台的 "Authentication" → "Users" 中手动创建第一个用户
2. 在SQL编辑器中执行：
   ```sql
   UPDATE user_profiles 
   SET role = 'super_admin' 
   WHERE email = '你的邮箱@example.com';
   ```

## 🎯 功能特色

### 权限管理
- **超级管理员**: 完整系统权限，包括用户管理
- **普通员工**: 业务数据管理权限

### 核心功能
- **商品管理**: CRUD操作、图片上传、自定义属性
- **客户管理**: 客户信息、多地址管理
- **订单管理**: 完整订单流程、状态跟踪
- **生产日历**: 可视化订单安排、节假日标记
- **数据分析**: 销售趋势、热门商品、实时统计

### 技术特性
- 响应式设计，支持多设备
- 实时数据同步
- 安全的行级权限控制
- 现代化UI界面

## 🆘 常见问题

**Q: 页面显示空白怎么办？**
A: 检查浏览器控制台是否有错误，通常是Supabase配置问题。

**Q: 演示模式下能保存数据吗？**
A: 不能，演示模式仅用于功能展示，需要配置Supabase才能保存数据。

**Q: 忘记管理员密码怎么办？**
A: 在Supabase控制台的Authentication页面可以重置用户密码。

**Q: 如何添加新的员工账户？**
A: 使用管理员账户登录，在"用户管理"页面可以创建新用户。

## 📞 技术支持

如需帮助，请查看：
- 项目文档：`开发指导文档.md`
- 数据库文档：`database/README.md`
- 项目总结：`项目完成总结.md`

---

🎉 **开始探索您的订单管理系统吧！**
