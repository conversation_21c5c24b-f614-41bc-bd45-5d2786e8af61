package models

import (
	"time"
)

// Product 商品模型
type Product struct {
	ID               int64                  `json:"id" xorm:"pk autoincr 'id'"`
	Name             string                 `json:"name" xorm:"varchar(255) notnull 'name'"`
	Category         string                 `json:"category" xorm:"varchar(255) 'category'"`
	Price            float64                `json:"price" xorm:"decimal(10,2) notnull 'price'"`
	IsListed         bool                   `json:"is_listed" xorm:"bool notnull default(true) 'is_listed'"`
	ImageURL         string                 `json:"image_url" xorm:"text 'image_url'"`
	CustomAttributes map[string]interface{} `json:"custom_attributes" xorm:"json 'custom_attributes'"`
	// 库存相关字段
	StockQuantity int       `json:"stock_quantity" xorm:"int notnull default(0) 'stock_quantity'"`
	MinStockLevel int       `json:"min_stock_level" xorm:"int notnull default(10) 'min_stock_level'"`
	MaxStockLevel int       `json:"max_stock_level" xorm:"int notnull default(1000) 'max_stock_level'"`
	StockUnit     string    `json:"stock_unit" xorm:"varchar(50) default('件') 'stock_unit'"`
	CreatedAt     time.Time `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt     time.Time `json:"updated_at" xorm:"updated 'updated_at'"`

	// 计算字段（不存储在数据库中）
	IsLowStock  bool   `json:"is_low_stock" xorm:"-"`
	StockStatus string `json:"stock_status" xorm:"-"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "products"
}

// ProductRequest 商品请求
type ProductRequest struct {
	Name             string                 `json:"name" binding:"required"`
	Category         string                 `json:"category"`
	Price            float64                `json:"price" binding:"required,gt=0"`
	IsListed         bool                   `json:"is_listed"`
	ImageURL         string                 `json:"image_url"`
	CustomAttributes map[string]interface{} `json:"custom_attributes"`
	StockQuantity    int                    `json:"stock_quantity"`
	MinStockLevel    int                    `json:"min_stock_level"`
	MaxStockLevel    int                    `json:"max_stock_level"`
	StockUnit        string                 `json:"stock_unit"`
}

// StockUpdateRequest 库存更新请求
type StockUpdateRequest struct {
	Quantity int    `json:"quantity" binding:"required"`
	Type     string `json:"type" binding:"required,oneof=in out set"` // in: 入库, out: 出库, set: 设置
	Reason   string `json:"reason"`
}
