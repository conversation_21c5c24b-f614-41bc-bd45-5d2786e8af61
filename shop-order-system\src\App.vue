<script setup>
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import Layout from './components/Layout.vue'

const route = useRoute()

// 不需要布局的页面（如登录页）
const noLayoutPages = ['/login']
const showLayout = computed(() => !noLayoutPages.includes(route.path))
</script>

<template>
  <div id="app">
    <!-- 登录页面不使用布局 -->
    <RouterView v-if="!showLayout" />

    <!-- 其他页面使用布局 -->
    <Layout v-else />
  </div>
</template>

<style>
/* CSS 变量定义 */
:root {
  --primary-color: #409eff;
  --primary-light: #ecf5ff;
  --primary-dark: #337ecc;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;

  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;

  --border-light: #ebeef5;
  --border-base: #dcdfe6;
  --border-dark: #d4d7de;

  --bg-color: #ffffff;
  --bg-page: #f0f2f5;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-base: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4px 20px rgba(0, 0, 0, 0.15);

  --border-radius-small: 4px;
  --border-radius-base: 6px;
  --border-radius-large: 8px;

  --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-fast: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: var(--bg-page);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100%;
  max-width: 100vw;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-page);
  border-radius: var(--border-radius-small);
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: var(--border-radius-small);
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

/* 通用动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 通用工具类 */
.fade-enter-active,
.fade-leave-active {
  transition: var(--transition-base);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.slide-enter-active,
.slide-leave-active {
  transition: var(--transition-base);
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

.text-primary {
  color: var(--text-primary);
}

.text-regular {
  color: var(--text-regular);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-placeholder {
  color: var(--text-placeholder);
}

.bg-primary {
  background-color: var(--primary-color);
}

.bg-success {
  background-color: var(--success-color);
}

.bg-warning {
  background-color: var(--warning-color);
}

.bg-danger {
  background-color: var(--danger-color);
}

.shadow-light {
  box-shadow: var(--shadow-light);
}

.shadow-base {
  box-shadow: var(--shadow-base);
}

.shadow-dark {
  box-shadow: var(--shadow-dark);
}

.border-radius-small {
  border-radius: var(--border-radius-small);
}

.border-radius-base {
  border-radius: var(--border-radius-base);
}

.border-radius-large {
  border-radius: var(--border-radius-large);
}

.transition-base {
  transition: var(--transition-base);
}

.transition-fast {
  transition: var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  :root {
    --border-radius-small: 3px;
    --border-radius-base: 5px;
    --border-radius-large: 7px;
  }
}

@media (max-width: 768px) {
  html, body {
    overflow-x: hidden;
  }

  #app {
    overflow-x: hidden;
  }

  :root {
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-base: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --shadow-dark: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  }
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
}

/* 打印样式 */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  #app {
    overflow: visible !important;
    height: auto !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-light: #000000;
    --border-base: #000000;
    --border-dark: #000000;
    --text-secondary: #000000;
    --text-placeholder: #666666;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
