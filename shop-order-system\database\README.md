# 数据库设置指南

## 概述
本目录包含小商店订单管理系统的数据库设计和配置文件。

## 文件说明

### 1. schema.sql
包含所有数据库表的创建语句，包括：
- `products` - 商品表
- `customers` - 客户表  
- `addresses` - 地址表
- `orders` - 订单表
- `order_items` - 订单商品关联表
- `user_profiles` - 用户档案表

### 2. rls_policies.sql
包含行级安全策略（RLS）的配置，实现基于角色的数据访问控制：
- 超级管理员（super_admin）：拥有所有数据的完全访问权限
- 普通员工（staff）：拥有业务数据的访问权限，但不能管理用户

### 3. sample_data.sql
包含示例数据，用于开发和测试环境。

## Supabase 设置步骤

### 1. 创建 Supabase 项目
1. 访问 [Supabase](https://supabase.com)
2. 创建新项目
3. 记录项目URL和API密钥

### 2. 执行数据库脚本
在Supabase控制台的SQL编辑器中按顺序执行：

1. **创建表结构**
   ```sql
   -- 复制并执行 schema.sql 的内容
   ```

2. **设置安全策略**
   ```sql
   -- 复制并执行 rls_policies.sql 的内容
   ```

3. **插入示例数据**（可选，仅用于开发环境）
   ```sql
   -- 复制并执行 sample_data.sql 的内容
   ```

### 3. 配置认证
1. 在Supabase控制台中启用邮箱认证
2. 配置邮件模板（可选）
3. 设置重定向URL

### 4. 创建超级管理员账户
1. 在Supabase控制台的认证页面手动创建第一个用户
2. 在SQL编辑器中将该用户设置为超级管理员：
   ```sql
   UPDATE user_profiles 
   SET role = 'super_admin' 
   WHERE email = '<EMAIL>';
   ```

### 5. 配置存储桶（用于图片上传）
1. 在Supabase控制台创建存储桶 `product-images`
2. 设置存储桶为公开访问
3. 配置存储策略允许认证用户上传

## 环境配置

### 1. 复制环境变量文件
```bash
cp .env.example .env
```

### 2. 填写Supabase配置
在 `.env` 文件中填写：
```env
VITE_SUPABASE_URL=你的Supabase项目URL
VITE_SUPABASE_ANON_KEY=你的Supabase匿名密钥
```

## 数据模型关系

```
users (Supabase Auth)
  ↓ (1:1)
user_profiles
  
customers
  ↓ (1:n)
addresses

customers
  ↓ (1:n)
orders
  ↓ (1:n)
order_items
  ↓ (n:1)
products
```

## 权限说明

### 超级管理员 (super_admin)
- 完全的数据访问权限
- 用户管理权限
- 系统配置权限

### 普通员工 (staff)
- 商品管理（增删改查）
- 客户管理（增删改查）
- 订单管理（增删改查）
- 无用户管理权限

## 注意事项

1. **生产环境**：不要执行 `sample_data.sql`
2. **备份**：定期备份数据库
3. **安全**：妥善保管API密钥
4. **更新**：数据库结构变更时需要迁移脚本
