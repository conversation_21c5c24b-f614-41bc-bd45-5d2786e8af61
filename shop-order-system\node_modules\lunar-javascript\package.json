{"name": "lunar-javascript", "version": "1.7.3", "description": "lunar是一款无第三方依赖的公历(阳历)、农历(阴历、老黄历)、佛历和道历工具，支持星座、儒略日、干支、生肖、节气、节日、彭祖百忌、每日宜忌、吉神宜趋、凶煞宜忌、吉神(喜神/福神/财神/阳贵神/阴贵神)方位、胎神方位、冲煞、纳音、星宿、八字、五行、十神、建除十二值星、青龙名堂等十二神、黄道日及吉凶等。lunar is a calendar library for Solar and Chinese Lunar.", "main": "index.js", "scripts": {"test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/6tail/lunar-javascript.git"}, "keywords": ["calendar", "lunar", "solar", "节假日", "星座", "儒略日", "干支", "生肖", "节气", "节日", "彭祖百忌", "每日宜忌", "时辰宜忌", "吉神宜趋", "凶煞宜忌", "喜神", "福神", "财神", "阳贵神", "阴贵神", "胎神", "冲煞", "纳音", "星宿", "八字", "五行", "十神", "建除十二值星", "青龙名堂等十二神", "黄道黑道日"], "author": "6tail <<EMAIL>> (https://6tail.cn)", "license": "MIT", "bugs": {"url": "https://github.com/6tail/lunar-javascript/issues"}, "homepage": "https://github.com/6tail/lunar-javascript", "devDependencies": {"jest": "^26.6.3"}}