package controllers

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"slices"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type UploadController struct {
	uploadDir string
	baseURL   string
}

func NewUploadController(uploadDir, baseURL string) *UploadController {
	// 确保上传目录存在
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		panic(fmt.Sprintf("Failed to create upload directory: %v", err))
	}

	return &UploadController{
		uploadDir: uploadDir,
		baseURL:   baseURL,
	}
}

// UploadImage 上传图片
func (uc *UploadController) UploadImage(c *gin.Context) {
	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No file uploaded",
		})
		return
	}
	defer file.Close()

	// 验证文件类型
	if !uc.isValidImageType(header.Filename) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid file type. Only JPG, JPEG, PNG, GIF, WebP, BMP, SVG are allowed",
		})
		return
	}

	// 验证文件内容
	if !uc.validateImageContent(file) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid file content. File may be corrupted or not a valid image",
		})
		return
	}

	// 重置文件指针
	file.Seek(0, 0)

	// 验证文件大小 (最大5MB)
	if header.Size > 5*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "File size too large. Maximum 5MB allowed",
		})
		return
	}

	// 生成唯一文件名
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), ext)

	// 创建子目录（按日期分组）
	dateDir := time.Now().Format("2006/01/02")
	fullDir := filepath.Join(uc.uploadDir, "images", dateDir)
	if err := os.MkdirAll(fullDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create directory",
		})
		return
	}

	// 保存文件
	filePath := filepath.Join(fullDir, filename)
	dst, err := os.Create(filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create file",
		})
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to save file",
		})
		return
	}

	// 生成访问URL
	relativePath := filepath.Join("images", dateDir, filename)
	fileURL := fmt.Sprintf("%s/uploads/%s", uc.baseURL, strings.ReplaceAll(relativePath, "\\", "/"))

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"filename": filename,
		"url":      fileURL,
		"size":     header.Size,
		"message":  "File uploaded successfully",
	})
}

// UploadMultipleImages 批量上传图片
func (uc *UploadController) UploadMultipleImages(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to parse multipart form",
		})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No files uploaded",
		})
		return
	}

	// 限制批量上传数量
	if len(files) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Too many files. Maximum 10 files allowed",
		})
		return
	}

	var results []gin.H
	var errors []string

	for _, fileHeader := range files {
		file, err := fileHeader.Open()
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to open file %s: %v", fileHeader.Filename, err))
			continue
		}

		// 验证文件类型
		if !uc.isValidImageType(fileHeader.Filename) {
			errors = append(errors, fmt.Sprintf("Invalid file type for %s", fileHeader.Filename))
			file.Close()
			continue
		}

		// 验证文件大小
		if fileHeader.Size > 5*1024*1024 {
			errors = append(errors, fmt.Sprintf("File %s is too large", fileHeader.Filename))
			file.Close()
			continue
		}

		// 生成唯一文件名
		ext := filepath.Ext(fileHeader.Filename)
		filename := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), ext)

		// 创建子目录
		dateDir := time.Now().Format("2006/01/02")
		fullDir := filepath.Join(uc.uploadDir, "images", dateDir)
		if err := os.MkdirAll(fullDir, 0755); err != nil {
			errors = append(errors, fmt.Sprintf("Failed to create directory for %s", fileHeader.Filename))
			file.Close()
			continue
		}

		// 保存文件
		filePath := filepath.Join(fullDir, filename)
		dst, err := os.Create(filePath)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to create file for %s", fileHeader.Filename))
			file.Close()
			continue
		}

		if _, err := io.Copy(dst, file); err != nil {
			errors = append(errors, fmt.Sprintf("Failed to save file %s", fileHeader.Filename))
			dst.Close()
			file.Close()
			continue
		}

		dst.Close()
		file.Close()

		// 生成访问URL
		relativePath := filepath.Join("images", dateDir, filename)
		fileURL := fmt.Sprintf("%s/uploads/%s", uc.baseURL, strings.ReplaceAll(relativePath, "\\", "/"))

		results = append(results, gin.H{
			"original_name": fileHeader.Filename,
			"filename":      filename,
			"url":           fileURL,
			"size":          fileHeader.Size,
		})
	}

	response := gin.H{
		"success": true,
		"files":   results,
		"count":   len(results),
	}

	if len(errors) > 0 {
		response["errors"] = errors
		response["error_count"] = len(errors)
	}

	c.JSON(http.StatusOK, response)
}

// DeleteFile 删除文件
func (uc *UploadController) DeleteFile(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Filename is required",
		})
		return
	}

	// 从URL中提取相对路径
	relativePath := c.Query("path")
	if relativePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "File path is required",
		})
		return
	}

	// 构建完整文件路径
	fullPath := filepath.Join(uc.uploadDir, relativePath)

	// 安全检查：确保文件在上传目录内
	if !strings.HasPrefix(fullPath, uc.uploadDir) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid file path",
		})
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "File not found",
		})
		return
	}

	// 删除文件
	if err := os.Remove(fullPath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete file",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "File deleted successfully",
	})
}

// isValidImageType 验证图片类型
func (uc *UploadController) isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".svg"}
	return slices.Contains(validExts, ext)
}

// validateImageContent 验证图片内容（防止恶意文件）
func (uc *UploadController) validateImageContent(file io.Reader) bool {
	// 读取文件头部字节来验证文件类型
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil {
		return false
	}

	// 检查文件魔数
	contentType := http.DetectContentType(buffer[:n])
	validTypes := []string{
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/bmp",
		"image/svg+xml",
	}

	return slices.Contains(validTypes, contentType)
}

// GetFileInfo 获取文件信息
func (uc *UploadController) GetFileInfo(c *gin.Context) {
	filename := c.Param("filename")
	relativePath := c.Query("path")

	if filename == "" || relativePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Filename and path are required",
		})
		return
	}

	fullPath := filepath.Join(uc.uploadDir, relativePath)

	// 安全检查
	if !strings.HasPrefix(fullPath, uc.uploadDir) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid file path",
		})
		return
	}

	// 获取文件信息
	fileInfo, err := os.Stat(fullPath)
	if os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "File not found",
		})
		return
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get file info",
		})
		return
	}

	fileURL := fmt.Sprintf("%s/uploads/%s", uc.baseURL, strings.ReplaceAll(relativePath, "\\", "/"))

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"filename":    filename,
		"url":         fileURL,
		"size":        fileInfo.Size(),
		"modified_at": fileInfo.ModTime(),
	})
}

// ListFiles 获取文件列表
func (uc *UploadController) ListFiles(c *gin.Context) {
	// 获取查询参数
	category := c.DefaultQuery("category", "images")

	// 构建目录路径
	dirPath := filepath.Join(uc.uploadDir, category)

	// 检查目录是否存在
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"files":   []gin.H{},
			"total":   0,
		})
		return
	}

	var files []gin.H

	// 遍历目录
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 验证是否为图片文件
		if !uc.isValidImageType(info.Name()) {
			return nil
		}

		// 计算相对路径
		relativePath, err := filepath.Rel(uc.uploadDir, path)
		if err != nil {
			return err
		}

		// 生成访问URL
		fileURL := fmt.Sprintf("%s/uploads/%s", uc.baseURL, strings.ReplaceAll(relativePath, "\\", "/"))

		files = append(files, gin.H{
			"filename":    info.Name(),
			"path":        relativePath,
			"url":         fileURL,
			"size":        info.Size(),
			"modified_at": info.ModTime(),
		})

		return nil
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to list files",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"files":   files,
		"total":   len(files),
	})
}
