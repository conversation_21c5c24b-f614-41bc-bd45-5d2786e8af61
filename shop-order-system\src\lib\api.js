// API 配置和工具函数
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'

// 获取认证token
const getAuthToken = () => {
  return localStorage.getItem('auth_token')
}

// 设置认证token
const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('auth_token', token)
  } else {
    localStorage.removeItem('auth_token')
  }
}

// 通用请求函数
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`
  const token = getAuthToken()
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }

  try {
    const response = await fetch(url, config)
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return { success: true, data: data.data || data }
  } catch (error) {
    console.error('API request failed:', error)
    return { success: false, error: error.message }
  }
}

// 认证相关API
export const authAPI = {
  // 登录
  login: async (email, password) => {
    return apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })
  },

  // 获取用户信息
  getProfile: async () => {
    return apiRequest('/user/profile')
  },

  // 修改密码
  changePassword: async (oldPassword, newPassword) => {
    return apiRequest('/user/password', {
      method: 'PUT',
      body: JSON.stringify({
        old_password: oldPassword,
        new_password: newPassword,
      }),
    })
  },

  // 注册用户（管理员功能）
  register: async (email, password, role = 'staff') => {
    return apiRequest('/admin/users', {
      method: 'POST',
      body: JSON.stringify({ email, password, role }),
    })
  },
}

// 客户相关API
export const customerAPI = {
  // 获取客户列表
  getCustomers: async () => {
    return apiRequest('/customers')
  },

  // 获取单个客户
  getCustomer: async (id) => {
    return apiRequest(`/customers/${id}`)
  },

  // 创建客户
  createCustomer: async (customerData) => {
    return apiRequest('/customers', {
      method: 'POST',
      body: JSON.stringify(customerData),
    })
  },

  // 更新客户
  updateCustomer: async (id, customerData) => {
    return apiRequest(`/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customerData),
    })
  },

  // 删除客户
  deleteCustomer: async (id) => {
    return apiRequest(`/customers/${id}`, {
      method: 'DELETE',
    })
  },
}

// 地址相关API
export const addressAPI = {
  // 获取客户地址列表
  getAddresses: async (customerId) => {
    return apiRequest(`/customers/${customerId}/addresses`)
  },

  // 创建地址
  createAddress: async (customerId, addressData) => {
    return apiRequest(`/customers/${customerId}/addresses`, {
      method: 'POST',
      body: JSON.stringify(addressData),
    })
  },

  // 更新地址
  updateAddress: async (id, addressData) => {
    return apiRequest(`/addresses/${id}`, {
      method: 'PUT',
      body: JSON.stringify(addressData),
    })
  },

  // 删除地址
  deleteAddress: async (id) => {
    return apiRequest(`/addresses/${id}`, {
      method: 'DELETE',
    })
  },

  // 设置默认地址
  setDefaultAddress: async (customerId, addressId) => {
    return apiRequest(`/customers/${customerId}/addresses/${addressId}/default`, {
      method: 'PUT',
    })
  },
}

// 商品相关API
export const productAPI = {
  // 获取商品列表
  getProducts: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/products?${queryString}` : '/products'
    return apiRequest(endpoint)
  },

  // 获取单个商品
  getProduct: async (id) => {
    return apiRequest(`/products/${id}`)
  },

  // 创建商品
  createProduct: async (productData) => {
    return apiRequest('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    })
  },

  // 更新商品
  updateProduct: async (id, productData) => {
    return apiRequest(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    })
  },

  // 删除商品
  deleteProduct: async (id) => {
    return apiRequest(`/products/${id}`, {
      method: 'DELETE',
    })
  },

  // 获取商品分类
  getCategories: async () => {
    return apiRequest('/products/categories')
  },

  // 更新商品库存
  updateStock: async (id, stockData) => {
    return apiRequest(`/products/${id}/stock`, {
      method: 'PUT',
      body: JSON.stringify(stockData),
    })
  },

  // 获取低库存商品
  getLowStockProducts: async () => {
    return apiRequest('/products/low-stock')
  },

  // 上传商品图片
  uploadImage: async (formData) => {
    return apiRequest('/upload/image', {
      method: 'POST',
      body: formData,
      // 不设置Content-Type，让浏览器自动设置multipart/form-data
      headers: {}
    })
  },

  // 批量上传图片
  uploadMultipleImages: async (formData) => {
    return apiRequest('/upload/images', {
      method: 'POST',
      body: formData,
      headers: {}
    })
  },

  // 删除图片
  deleteImage: async (filename, path) => {
    const queryParams = new URLSearchParams({ path }).toString()
    return apiRequest(`/upload/file/${filename}?${queryParams}`, {
      method: 'DELETE'
    })
  },

  // 获取文件信息
  getFileInfo: async (filename, path) => {
    const queryParams = new URLSearchParams({ path }).toString()
    return apiRequest(`/upload/file/${filename}?${queryParams}`)
  },

  // 获取文件列表
  getFileList: async (category = 'images') => {
    const queryParams = new URLSearchParams({ category }).toString()
    return apiRequest(`/upload/files?${queryParams}`)
  }
}

// 订单相关API
export const orderAPI = {
  // 获取订单列表
  getOrders: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/orders?${queryString}` : '/orders'
    return apiRequest(endpoint)
  },

  // 获取单个订单
  getOrder: async (id) => {
    return apiRequest(`/orders/${id}`)
  },

  // 创建订单
  createOrder: async (orderData) => {
    return apiRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    })
  },

  // 更新订单
  updateOrder: async (id, orderData) => {
    return apiRequest(`/orders/${id}`, {
      method: 'PUT',
      body: JSON.stringify(orderData),
    })
  },

  // 删除订单
  deleteOrder: async (id) => {
    return apiRequest(`/orders/${id}`, {
      method: 'DELETE',
    })
  },

  // 获取订单统计
  getStatistics: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/orders/statistics?${queryString}` : '/orders/statistics'
    return apiRequest(endpoint)
  },

  // 获取每日订单汇总
  getDailySummary: async (date) => {
    return apiRequest(`/orders/summary/daily/${date}`)
  },

  // 获取月度订单汇总
  getMonthlySummary: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/orders/summary/monthly?${queryString}` : '/orders/summary/monthly'
    return apiRequest(endpoint)
  },

  // 获取分类趋势
  getCategoryTrend: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/orders/trend/category?${queryString}` : '/orders/trend/category'
    return apiRequest(endpoint)
  },
}

// 节假日相关API
export const holidayAPI = {
  // 获取节假日列表
  getHolidays: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/holidays?${queryString}` : '/holidays'
    return apiRequest(endpoint)
  },

  // 获取指定日期的节假日
  getHolidayByDate: async (date) => {
    return apiRequest(`/holidays/${date}`)
  },

  // 获取日期范围内的节假日
  getHolidaysInRange: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/holidays/range?${queryString}` : '/holidays/range'
    return apiRequest(endpoint)
  },

  // 创建节假日
  createHoliday: async (holidayData) => {
    return apiRequest('/holidays', {
      method: 'POST',
      body: JSON.stringify(holidayData),
    })
  },

  // 更新节假日
  updateHoliday: async (id, holidayData) => {
    return apiRequest(`/holidays/${id}`, {
      method: 'PUT',
      body: JSON.stringify(holidayData),
    })
  },

  // 删除节假日
  deleteHoliday: async (id) => {
    return apiRequest(`/holidays/${id}`, {
      method: 'DELETE',
    })
  },

  // 同步节假日数据
  syncHolidays: async (year) => {
    return apiRequest('/holidays/sync', {
      method: 'POST',
      body: JSON.stringify({ year }),
    })
  },
}

// 农历日历相关API
export const calendarAPI = {
  // 公历转农历
  solarToLunar: async (date) => {
    return apiRequest(`/calendar/lunar?date=${date}`)
  },

  // 获取日历信息
  getCalendarInfo: async (date) => {
    return apiRequest(`/calendar/info?date=${date}`)
  },

  // 获取月度日历
  getMonthCalendar: async (year, month) => {
    return apiRequest(`/calendar/month?year=${year}&month=${month}`)
  },

  // 获取年度日历
  getYearCalendar: async (year) => {
    return apiRequest(`/calendar/year?year=${year}`)
  },
}

// 定时任务相关API
export const schedulerAPI = {
  // 获取定时任务状态
  getStatus: async () => {
    return apiRequest('/scheduler/status')
  },

  // 获取任务列表
  getJobs: async () => {
    return apiRequest('/scheduler/jobs')
  },

  // 立即执行任务
  runJob: async (jobName) => {
    return apiRequest(`/scheduler/jobs/${jobName}/run`, {
      method: 'POST',
    })
  },

  // 添加自定义任务
  addCustomJob: async (jobData) => {
    return apiRequest('/scheduler/jobs', {
      method: 'POST',
      body: JSON.stringify(jobData),
    })
  },

  // 删除任务
  removeJob: async (jobId) => {
    return apiRequest(`/scheduler/jobs/${jobId}`, {
      method: 'DELETE',
    })
  }
}

// 工具函数
export { getAuthToken, setAuthToken }
