package controllers

import (
	"net/http"
	"strconv"
	"time"

	"shop-order-backend/internal/models"
	"shop-order-backend/internal/services"

	"github.com/gin-gonic/gin"
	"xorm.io/xorm"
)

type LunarController struct {
	lunarService   *services.LunarService
	holidayService *services.HolidayService
}

func NewLunarController(db *xorm.Engine) *LunarController {
	return &LunarController{
		lunarService:   services.NewLunarService(),
		holidayService: services.NewHolidayService(db),
	}
}

// SolarToLunar 公历转农历
func (lc *LunarController) SolarToLunar(c *gin.Context) {
	dateStr := c.Query("date")
	if dateStr == "" {
		dateStr = time.Now().Format("2006-01-02")
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"error": "Invalid date format, use YYYY-MM-DD",
		})
		return
	}

	lunar, err := lc.lunarService.SolarToLunar(date)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"error": "Failed to convert to lunar date",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":       lunar,
		"solar_date": dateStr,
	})
}

// GetCalendarInfo 获取日历信息
func (lc *LunarController) GetCalendarInfo(c *gin.Context) {
	dateStr := c.Query("date")
	if dateStr == "" {
		dateStr = time.Now().Format("2006-01-02")
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid date format, use YYYY-MM-DD",
		})
		return
	}

	// 获取节假日信息
	holiday, err := lc.holidayService.GetHolidayByDate(date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch holiday info",
		})
		return
	}

	// 获取完整日历信息
	calendarInfo, err := lc.lunarService.GetCalendarInfo(date, holiday)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get calendar info",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": calendarInfo,
	})
}

// GetMonthCalendar 获取整月日历
func (lc *LunarController) GetMonthCalendar(c *gin.Context) {
	yearStr := c.Query("year")
	monthStr := c.Query("month")

	// 默认为当前年月
	now := time.Now()
	year := now.Year()
	month := int(now.Month())

	if yearStr != "" {
		if y, err := strconv.Atoi(yearStr); err == nil {
			year = y
		}
	}

	if monthStr != "" {
		if m, err := strconv.Atoi(monthStr); err == nil && m >= 1 && m <= 12 {
			month = m
		}
	}

	// 获取该月的节假日
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, -1)

	holidays, err := lc.holidayService.GetHolidaysInRange(startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch holidays",
		})
		return
	}

	// 获取整月日历
	calendar, err := lc.lunarService.GetMonthCalendar(year, month, holidays)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get month calendar",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":       calendar,
		"year":       year,
		"month":      month,
		"total_days": len(calendar),
	})
}

// GetYearCalendar 获取整年日历概览
func (lc *LunarController) GetYearCalendar(c *gin.Context) {
	yearStr := c.Query("year")
	year := time.Now().Year()

	if yearStr != "" {
		if y, err := strconv.Atoi(yearStr); err == nil {
			year = y
		}
	}

	// 获取该年的所有节假日
	holidays, err := lc.holidayService.GetHolidays(year)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch holidays",
		})
		return
	}

	// 按月组织数据
	yearCalendar := make(map[string]interface{})

	for month := 1; month <= 12; month++ {
		// 获取该月的节假日
		monthHolidays := []*models.Holiday{}
		for _, holiday := range holidays {
			if int(holiday.Date.Month()) == month {
				monthHolidays = append(monthHolidays, holiday)
			}
		}

		// 获取该月日历
		calendar, err := lc.lunarService.GetMonthCalendar(year, month, monthHolidays)
		if err != nil {
			continue
		}

		monthKey := strconv.Itoa(month)
		yearCalendar[monthKey] = map[string]interface{}{
			"month": month,
			"days":  calendar,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": yearCalendar,
		"year": year,
	})
}
