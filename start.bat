@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 小商店订单管理系统启动脚本 (Windows)

set "command=%~1"
set "option=%~2"

if "%command%"=="" set "command=start"
if "%option%"=="" set "option=sqlite"

goto :main

:print_success
echo [92m%~1[0m
goto :eof

:print_error
echo [91m%~1[0m
goto :eof

:print_warning
echo [93m%~1[0m
goto :eof

:print_info
echo [94m%~1[0m
goto :eof

:check_docker
where docker >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker 未安装，请先安装 Docker Desktop"
    exit /b 1
)

where docker-compose >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit /b 1
)
goto :eof

:create_directories
call :print_info "创建必要的目录..."
if not exist "backend\data" mkdir "backend\data"
if not exist "backend\uploads" mkdir "backend\uploads"
goto :eof

:setup_env_files
call :print_info "设置环境变量文件..."

if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env" >nul
    call :print_success "已创建 backend\.env 文件"
) else (
    call :print_info "backend\.env 文件已存在"
)

if not exist "shop-order-system\.env" (
    echo VITE_API_BASE_URL=http://localhost:8080/api > "shop-order-system\.env"
    call :print_success "已创建 shop-order-system\.env 文件"
) else (
    call :print_info "shop-order-system\.env 文件已存在"
)
goto :eof

:start_services
set "mode=%~1"
call :print_info "启动服务 (模式: %mode%)..."

if "%mode%"=="sqlite" (
    docker-compose up -d
) else if "%mode%"=="mysql" (
    docker-compose --profile mysql up -d
) else (
    call :print_error "未知模式: %mode%"
    exit /b 1
)
goto :eof

:wait_for_services
call :print_info "等待服务启动..."

set /a max_attempts=30
set /a attempt=0

:wait_backend
if %attempt% geq %max_attempts% (
    call :print_error "后端服务启动超时"
    exit /b 1
)

curl -s http://localhost:8080/health >nul 2>&1
if errorlevel 1 (
    set /a attempt+=1
    call :print_info "等待后端服务启动... (!attempt!/%max_attempts%)"
    timeout /t 2 /nobreak >nul
    goto :wait_backend
)

call :print_success "后端服务已启动"

set /a attempt=0

:wait_frontend
if %attempt% geq %max_attempts% (
    call :print_error "前端服务启动超时"
    exit /b 1
)

curl -s http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    set /a attempt+=1
    call :print_info "等待前端服务启动... (!attempt!/%max_attempts%)"
    timeout /t 2 /nobreak >nul
    goto :wait_frontend
)

call :print_success "前端服务已启动"
goto :eof

:show_service_info
call :print_success "🎉 服务启动成功！"
echo.
call :print_info "服务地址："
echo   前端应用: http://localhost:3000
echo   后端API:  http://localhost:8080
echo   健康检查: http://localhost:8080/health
echo.
call :print_info "默认账户："
echo   管理员: <EMAIL> / demo123
echo   员工:   <EMAIL> / demo123
echo.
call :print_info "常用命令："
echo   查看日志: docker-compose logs -f
echo   停止服务: docker-compose down
echo   重启服务: docker-compose restart
goto :eof

:stop_services
call :print_info "停止服务..."
docker-compose down
call :print_success "服务已停止"
goto :eof

:show_logs
set "service=%~1"
if "%service%"=="" (
    docker-compose logs -f
) else (
    docker-compose logs -f "%service%"
)
goto :eof

:show_status
call :print_info "服务状态："
docker-compose ps
goto :eof

:show_help
echo 小商店订单管理系统启动脚本
echo.
echo 用法: %~nx0 [命令] [选项]
echo.
echo 命令:
echo   start [sqlite^|mysql]  启动服务 (默认: sqlite)
echo   stop                  停止服务
echo   restart [sqlite^|mysql] 重启服务
echo   logs [service]        查看日志
echo   status                查看服务状态
echo   help                  显示帮助信息
echo.
echo 示例:
echo   %~nx0 start              # 使用 SQLite 启动
echo   %~nx0 start mysql        # 使用 MySQL 启动
echo   %~nx0 logs backend       # 查看后端日志
echo   %~nx0 stop               # 停止所有服务
goto :eof

:main
if "%command%"=="start" (
    call :check_docker
    call :create_directories
    call :setup_env_files
    call :start_services "%option%"
    call :wait_for_services
    call :show_service_info
) else if "%command%"=="stop" (
    call :stop_services
) else if "%command%"=="restart" (
    call :stop_services
    timeout /t 2 /nobreak >nul
    call :start_services "%option%"
    call :wait_for_services
    call :show_service_info
) else if "%command%"=="logs" (
    call :show_logs "%option%"
) else if "%command%"=="status" (
    call :show_status
) else if "%command%"=="help" (
    call :show_help
) else (
    call :print_error "未知命令: %command%"
    call :show_help
    exit /b 1
)

goto :eof
