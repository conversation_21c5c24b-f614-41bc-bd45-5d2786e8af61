package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"shop-order-backend/internal/models"

	"xorm.io/xorm"
)

type HolidayService struct {
	db *xorm.Engine
}

func NewHolidayService(db *xorm.Engine) *HolidayService {
	return &HolidayService{db: db}
}

// SyncHolidays 同步指定年份的节假日数据
func (s *HolidayService) SyncHolidays(year int) error {
	// 先删除该年份的现有数据
	_, err := s.db.Where("year = ?", year).Delete(&models.Holiday{})
	if err != nil {
		return fmt.Errorf("failed to delete existing holidays: %v", err)
	}

	// 从第三方API获取数据
	holidays, err := s.fetchHolidaysFromAPI(year)
	if err != nil {
		return fmt.Errorf("failed to fetch holidays from API: %v", err)
	}

	// 批量插入数据库
	if len(holidays) > 0 {
		_, err = s.db.Insert(holidays)
		if err != nil {
			return fmt.Errorf("failed to insert holidays: %v", err)
		}
	}

	return nil
}

// fetchHolidaysFromAPI 从第三方API获取节假日数据
func (s *HolidayService) fetchHolidaysFromAPI(year int) ([]*models.Holiday, error) {
	// 使用免费的节假日API
	url := fmt.Sprintf("https://timor.tech/api/holiday/year/%d", year)

	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var apiResp models.ThirdPartyHolidayAPI
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}

	if apiResp.Code != 0 {
		return nil, fmt.Errorf("API returned error code: %d", apiResp.Code)
	}

	var holidays []*models.Holiday
	for _, h := range apiResp.Data.Holidays {
		date, err := time.Parse("2006-01-02", h.Date)
		if err != nil {
			continue
		}

		holidayType := "workday"
		if h.IsOff {
			holidayType = "holiday"
		}

		holiday := &models.Holiday{
			Date:       date,
			Name:       h.Name,
			Type:       holidayType,
			IsOfficial: true,
			Year:       year,
		}
		holidays = append(holidays, holiday)
	}

	return holidays, nil
}

// GetHolidays 获取指定年份的节假日
func (s *HolidayService) GetHolidays(year int) ([]*models.Holiday, error) {
	var holidays []*models.Holiday
	err := s.db.Where("year = ?", year).OrderBy("date").Find(&holidays)
	return holidays, err
}

// GetHolidayByDate 获取指定日期的节假日信息
func (s *HolidayService) GetHolidayByDate(date time.Time) (*models.Holiday, error) {
	var holiday models.Holiday
	has, err := s.db.Where("date = ?", date.Format("2006-01-02")).Get(&holiday)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &holiday, nil
}

// IsHoliday 检查指定日期是否为节假日
func (s *HolidayService) IsHoliday(date time.Time) (bool, error) {
	holiday, err := s.GetHolidayByDate(date)
	if err != nil {
		return false, err
	}
	return holiday != nil && holiday.Type == "holiday", nil
}

// GetHolidaysInRange 获取指定日期范围内的节假日
func (s *HolidayService) GetHolidaysInRange(startDate, endDate time.Time) ([]*models.Holiday, error) {
	var holidays []*models.Holiday
	err := s.db.Where("date >= ? AND date <= ?",
		startDate.Format("2006-01-02"),
		endDate.Format("2006-01-02")).
		OrderBy("date").Find(&holidays)
	return holidays, err
}

// CreateHoliday 创建自定义节假日
func (s *HolidayService) CreateHoliday(holiday *models.Holiday) error {
	holiday.Year = holiday.Date.Year()
	_, err := s.db.Insert(holiday)
	return err
}

// UpdateHoliday 更新节假日信息
func (s *HolidayService) UpdateHoliday(id int64, updates *models.Holiday) error {
	updates.Year = updates.Date.Year()
	_, err := s.db.ID(id).Update(updates)
	return err
}

// DeleteHoliday 删除节假日
func (s *HolidayService) DeleteHoliday(id int64) error {
	_, err := s.db.ID(id).Delete(&models.Holiday{})
	return err
}

// AutoSyncCurrentYear 自动同步当前年份的节假日
func (s *HolidayService) AutoSyncCurrentYear() error {
	currentYear := time.Now().Year()
	return s.SyncHolidays(currentYear)
}

// AutoSyncNextYear 自动同步下一年的节假日（用于年底提前同步）
func (s *HolidayService) AutoSyncNextYear() error {
	nextYear := time.Now().Year() + 1
	return s.SyncHolidays(nextYear)
}
