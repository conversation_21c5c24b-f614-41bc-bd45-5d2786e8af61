package main

import (
	"fmt"
	"log"
	"shop-order-backend/internal/config"
	"shop-order-backend/internal/database"
	"shop-order-backend/internal/models"

	"golang.org/x/crypto/bcrypt"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Init(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 检查是否已存在管理员用户
	var existingUser models.User
	has, err := db.Where("email = ?", "<EMAIL>").Get(&existingUser)
	if err != nil {
		log.Fatal("Failed to check existing user:", err)
	}

	if has {
		fmt.Println("Admin user already exists!")
		return
	}

	// 创建管理员用户
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
	if err != nil {
		log.Fatal("Failed to hash password:", err)
	}

	admin := &models.User{
		Email:    "<EMAIL>",
		Password: string(hashedPassword),
		Role:     "super_admin",
		IsActive: true,
	}

	_, err = db.Insert(admin)
	if err != nil {
		log.Fatal("Failed to create admin user:", err)
	}

	fmt.Println("Admin user created successfully!")
	fmt.Println("Email: <EMAIL>")
	fmt.Println("Password: admin123")
}
