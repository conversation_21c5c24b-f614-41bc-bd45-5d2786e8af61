package controllers

import (
	"net/http"
	"strconv"

	"shop-order-backend/internal/models"

	"github.com/gin-gonic/gin"
	"xorm.io/xorm"
)

type ProductController struct {
	db *xorm.Engine
}

func NewProductController(db *xorm.Engine) *ProductController {
	return &ProductController{db: db}
}

// GetProducts 获取商品列表
func (pc *ProductController) GetProducts(c *gin.Context) {
	var products []models.Product

	// 查询参数
	category := c.Query("category")
	isListed := c.Query("is_listed")
	lowStock := c.Query("low_stock") == "true"
	search := c.Query("search")

	session := pc.db.NewSession()
	defer session.Close()

	// 根据查询参数过滤
	if category != "" {
		session = session.Where("category = ?", category)
	}

	if isListed != "" {
		if isListed == "true" {
			session = session.Where("is_listed = ?", true)
		} else if isListed == "false" {
			session = session.Where("is_listed = ?", false)
		}
	}

	if search != "" {
		session = session.Where("name LIKE ?", "%"+search+"%")
	}

	if lowStock {
		session = session.Where("stock_quantity <= min_stock_level")
	}

	if err := session.OrderBy("created_at DESC").Find(&products); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch products",
		})
		return
	}

	// 计算库存状态
	for i := range products {
		pc.calculateStockStatus(&products[i])
	}

	c.JSON(http.StatusOK, gin.H{
		"data":    products,
		"success": true,
	})
}

// GetProduct 获取单个商品信息
func (pc *ProductController) GetProduct(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid product ID",
		})
		return
	}

	var product models.Product
	has, err := pc.db.ID(id).Get(&product)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Product not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": product,
	})
}

// CreateProduct 创建商品
func (pc *ProductController) CreateProduct(c *gin.Context) {
	var req models.ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	product := &models.Product{
		Name:             req.Name,
		Category:         req.Category,
		Price:            req.Price,
		IsListed:         req.IsListed,
		ImageURL:         req.ImageURL,
		CustomAttributes: req.CustomAttributes,
		StockQuantity:    req.StockQuantity,
		MinStockLevel:    req.MinStockLevel,
		MaxStockLevel:    req.MaxStockLevel,
		StockUnit:        req.StockUnit,
	}

	// 设置默认值
	if product.MinStockLevel == 0 {
		product.MinStockLevel = 10
	}
	if product.MaxStockLevel == 0 {
		product.MaxStockLevel = 1000
	}
	if product.StockUnit == "" {
		product.StockUnit = "件"
	}

	if _, err := pc.db.Insert(product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create product",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"data": product,
	})
}

// UpdateProduct 更新商品信息
func (pc *ProductController) UpdateProduct(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid product ID",
		})
		return
	}

	var req models.ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 检查商品是否存在
	var product models.Product
	has, err := pc.db.ID(id).Get(&product)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Product not found",
		})
		return
	}

	// 更新商品信息
	product.Name = req.Name
	product.Category = req.Category
	product.Price = req.Price
	product.IsListed = req.IsListed
	product.ImageURL = req.ImageURL
	product.CustomAttributes = req.CustomAttributes

	// 更新库存相关字段（如果提供了值）
	if req.MinStockLevel > 0 {
		product.MinStockLevel = req.MinStockLevel
	}
	if req.MaxStockLevel > 0 {
		product.MaxStockLevel = req.MaxStockLevel
	}
	if req.StockUnit != "" {
		product.StockUnit = req.StockUnit
	}

	if _, err := pc.db.ID(id).Update(&product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update product",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": product,
	})
}

// DeleteProduct 删除商品
func (pc *ProductController) DeleteProduct(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid product ID",
		})
		return
	}

	// 检查商品是否存在
	var product models.Product
	has, err := pc.db.ID(id).Get(&product)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Product not found",
		})
		return
	}

	// 删除商品
	if _, err := pc.db.ID(id).Delete(&models.Product{}); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete product",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Product deleted successfully",
	})
}

// GetCategories 获取商品分类列表
func (pc *ProductController) GetCategories(c *gin.Context) {
	var categories []string

	if err := pc.db.Table("products").Distinct("category").Where("category != ''").Find(&categories); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch categories",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": categories,
	})
}

// UpdateStock 更新商品库存
func (pc *ProductController) UpdateStock(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid product ID",
		})
		return
	}

	var req models.StockUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 检查商品是否存在
	var product models.Product
	has, err := pc.db.ID(id).Get(&product)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Product not found",
		})
		return
	}

	// 计算新的库存数量
	var newQuantity int
	switch req.Type {
	case "in": // 入库
		newQuantity = product.StockQuantity + req.Quantity
	case "out": // 出库
		newQuantity = product.StockQuantity - req.Quantity
		if newQuantity < 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Insufficient stock",
			})
			return
		}
	case "set": // 设置
		newQuantity = req.Quantity
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid update type",
		})
		return
	}

	// 更新库存
	product.StockQuantity = newQuantity
	if _, err := pc.db.ID(id).Cols("stock_quantity").Update(&product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update stock",
		})
		return
	}

	// 计算库存状态
	pc.calculateStockStatus(&product)

	c.JSON(http.StatusOK, gin.H{
		"data":    product,
		"message": "Stock updated successfully",
		"success": true,
	})
}

// GetLowStockProducts 获取低库存商品
func (pc *ProductController) GetLowStockProducts(c *gin.Context) {
	var products []models.Product

	err := pc.db.Where("stock_quantity <= min_stock_level").OrderBy("stock_quantity ASC").Find(&products)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch low stock products",
		})
		return
	}

	// 计算库存状态
	for i := range products {
		pc.calculateStockStatus(&products[i])
	}

	c.JSON(http.StatusOK, gin.H{
		"data":    products,
		"count":   len(products),
		"success": true,
	})
}

// calculateStockStatus 计算库存状态
func (pc *ProductController) calculateStockStatus(product *models.Product) {
	if product.StockQuantity <= 0 {
		product.StockStatus = "out_of_stock"
		product.IsLowStock = true
	} else if product.StockQuantity <= product.MinStockLevel {
		product.StockStatus = "low_stock"
		product.IsLowStock = true
	} else if product.StockQuantity >= product.MaxStockLevel {
		product.StockStatus = "overstock"
		product.IsLowStock = false
	} else {
		product.StockStatus = "normal"
		product.IsLowStock = false
	}
}
