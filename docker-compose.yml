version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - GIN_MODE=release
      - DB_TYPE=sqlite
      - SQLITE_PATH=/app/data/shop_order.db
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_EXPIRE_HOURS=24
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173
    volumes:
      - backend_data:/app/data
      - backend_uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./shop-order-system
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - VITE_API_BASE_URL=http://localhost:8080/api
    depends_on:
      - backend
    restart: unless-stopped

  # MySQL数据库（生产环境可选）
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=shop_order
      - MYSQL_USER=shop_user
      - MYSQL_PASSWORD=shop_password
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    profiles:
      - mysql

volumes:
  backend_data:
  backend_uploads:
  mysql_data:
