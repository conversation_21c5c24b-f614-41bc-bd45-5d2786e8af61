# 小商店订单管理系统

一个为小型实体商店设计的轻量级订单管理工具，支持从接单、管理商品/客户、安排生产到交付的全过程。

## 技术栈

### 前端
- **框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **图表库**: ECharts + Vue-ECharts
- **构建工具**: Vite

### 后端
- **语言**: Go 1.21+
- **Web框架**: Gin
- **ORM**: XORM
- **数据库**: SQLite (开发) / MySQL (生产)
- **认证**: JWT
- **CORS**: gin-contrib/cors

## 功能特性

- ✅ **用户认证**: JWT认证，支持管理员和员工角色
- ✅ **客户管理**: 客户信息管理，支持多地址绑定
- ✅ **商品管理**: 商品CRUD，分类管理，自定义属性
- ✅ **订单管理**: 订单录入、状态跟踪、付款管理
- ✅ **数据统计**: 销售统计、订单分析、图表展示
- ✅ **移动端适配**: 响应式设计，支持移动设备
- ✅ **Docker部署**: 支持容器化部署

## 快速开始

### 方式一：Docker 部署（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd shop-order-system
```

2. **使用 Docker Compose 启动**
```bash
# 使用 SQLite（默认）
docker-compose up -d

# 使用 MySQL
docker-compose --profile mysql up -d
```

3. **访问应用**
- 前端: http://localhost:3000
- 后端API: http://localhost:8080
- 健康检查: http://localhost:8080/health

### 方式二：本地开发

#### 后端启动

1. **进入后端目录**
```bash
cd backend
```

2. **安装依赖**
```bash
go mod download
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库等信息
```

4. **启动后端服务**
```bash
go run main.go
```

#### 前端启动

1. **进入前端目录**
```bash
cd shop-order-system
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 设置 VITE_API_BASE_URL=http://localhost:8080/api
```

4. **启动开发服务器**
```bash
npm run dev
```

## 默认账户

系统会自动创建以下默认账户：

- **管理员**: <EMAIL> / demo123
- **员工**: <EMAIL> / demo123

## 环境配置

### 后端环境变量

```env
# 服务器配置
PORT=8080
GIN_MODE=debug

# 数据库配置
DB_TYPE=sqlite  # sqlite 或 mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=shop_order
DB_USER=root
DB_PASSWORD=
DB_CHARSET=utf8mb4

# SQLite 配置
SQLITE_PATH=./data/shop_order.db

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE_HOURS=24

# CORS 配置
CORS_ORIGINS=http://localhost:5173,http://localhost:3000
```

### 前端环境变量

```env
# API 基础URL
VITE_API_BASE_URL=http://localhost:8080/api
```

## API 文档

### 认证相关
- `POST /api/auth/login` - 用户登录
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/password` - 修改密码

### 客户管理
- `GET /api/customers` - 获取客户列表
- `POST /api/customers` - 创建客户
- `GET /api/customers/:id` - 获取客户详情
- `PUT /api/customers/:id` - 更新客户
- `DELETE /api/customers/:id` - 删除客户

### 地址管理
- `GET /api/customers/:customer_id/addresses` - 获取客户地址
- `POST /api/customers/:customer_id/addresses` - 创建地址
- `PUT /api/addresses/:id` - 更新地址
- `DELETE /api/addresses/:id` - 删除地址

### 商品管理
- `GET /api/products` - 获取商品列表
- `POST /api/products` - 创建商品
- `GET /api/products/:id` - 获取商品详情
- `PUT /api/products/:id` - 更新商品
- `DELETE /api/products/:id` - 删除商品

### 订单管理
- `GET /api/orders` - 获取订单列表
- `POST /api/orders` - 创建订单
- `GET /api/orders/:id` - 获取订单详情
- `PUT /api/orders/:id` - 更新订单
- `DELETE /api/orders/:id` - 删除订单

## 数据库迁移

系统启动时会自动创建数据库表结构。如需手动迁移：

### SQLite 到 MySQL

1. 修改 `.env` 文件中的数据库配置
2. 重启后端服务，系统会自动创建MySQL表结构
3. 手动迁移数据（如需要）

## 部署说明

### 生产环境部署

1. **修改环境变量**
```bash
# 后端
GIN_MODE=release
DB_TYPE=mysql
JWT_SECRET=your-production-secret

# 前端
VITE_API_BASE_URL=https://your-domain.com/api
```

2. **构建和部署**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### 反向代理配置

建议使用 Nginx 作为反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
    }

    location /api/ {
        proxy_pass http://localhost:8080/api/;
    }
}
```

## 开发指南

### 添加新功能

1. **后端**: 在 `backend/internal/controllers` 添加控制器
2. **前端**: 在 `shop-order-system/src/views` 添加页面
3. **API**: 在 `shop-order-system/src/lib/api.js` 添加API调用
4. **状态管理**: 在 `shop-order-system/src/stores` 添加store

### 代码规范

- 后端遵循 Go 标准代码规范
- 前端使用 ESLint + Prettier
- 提交前请运行测试

## 故障排除

### 常见问题

1. **端口冲突**: 修改 docker-compose.yml 中的端口映射
2. **数据库连接失败**: 检查数据库配置和网络连接
3. **CORS 错误**: 确认 CORS_ORIGINS 配置正确
4. **JWT 认证失败**: 检查 JWT_SECRET 配置

### 日志查看

```bash
# 查看容器日志
docker-compose logs backend
docker-compose logs frontend

# 实时查看日志
docker-compose logs -f backend
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
