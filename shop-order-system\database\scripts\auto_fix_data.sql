-- 自动数据修复脚本
-- 执行前请先备份数据库

BEGIN;

-- 创建修复日志表
CREATE TABLE IF NOT EXISTS data_fix_log (
    id SERIAL PRIMARY KEY,
    fix_type VARCHAR(100) NOT NULL,
    description TEXT,
    records_affected INTEGER DEFAULT 0,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    success BOOLEAN DEFAULT true,
    error_message TEXT
);

-- 1. 修复客户数据
DO $$
DECLARE
    affected_count INTEGER := 0;
BEGIN
    -- 修复空的客户姓名
    UPDATE customers 
    SET name = '客户' || id 
    WHERE name IS NULL OR name = '';
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('customer_names', '修复空的客户姓名', affected_count);
    
    RAISE NOTICE '修复了 % 个客户的姓名', affected_count;
END $$;

-- 2. 修复地址数据
DO $$
DECLARE
    affected_count INTEGER := 0;
BEGIN
    -- 修复空的收货人姓名（使用客户姓名）
    UPDATE addresses 
    SET recipient_name = c.name
    FROM customers c
    WHERE addresses.customer_id = c.id 
    AND (addresses.recipient_name IS NULL OR addresses.recipient_name = '');
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('address_recipient_names', '修复空的收货人姓名', affected_count);
    
    RAISE NOTICE '修复了 % 个地址的收货人姓名', affected_count;
END $$;

-- 3. 修复默认地址问题
DO $$
DECLARE
    affected_count INTEGER := 0;
    total_fixed INTEGER := 0;
BEGIN
    -- 首先移除多余的默认地址（保留最新的）
    UPDATE addresses 
    SET is_default = false 
    WHERE id IN (
        SELECT a.id
        FROM addresses a
        WHERE a.is_default = true
        AND a.id NOT IN (
            SELECT DISTINCT ON (customer_id) id
            FROM addresses
            WHERE is_default = true
            ORDER BY customer_id, created_at DESC
        )
    );
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    total_fixed := affected_count;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('remove_multiple_defaults', '移除多余的默认地址', affected_count);
    
    -- 为没有默认地址的客户设置默认地址
    UPDATE addresses 
    SET is_default = true 
    WHERE id IN (
        SELECT DISTINCT ON (customer_id) id
        FROM addresses a1
        WHERE NOT EXISTS (
            SELECT 1 FROM addresses a2 
            WHERE a2.customer_id = a1.customer_id 
            AND a2.is_default = true
        )
        ORDER BY customer_id, created_at ASC
    );
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    total_fixed := total_fixed + affected_count;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('set_default_addresses', '为客户设置默认地址', affected_count);
    
    RAISE NOTICE '修复了 % 个默认地址问题', total_fixed;
END $$;

-- 4. 修复商品数据
DO $$
DECLARE
    affected_count INTEGER := 0;
BEGIN
    -- 修复空的商品名称
    UPDATE products 
    SET name = '商品' || id 
    WHERE name IS NULL OR name = '';
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('product_names', '修复空的商品名称', affected_count);
    
    -- 修复无效的商品价格
    UPDATE products 
    SET price = 0.01 
    WHERE price IS NULL OR price <= 0;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('product_prices', '修复无效的商品价格', affected_count);
    
    -- 修复负库存
    UPDATE products 
    SET stock_quantity = 0 
    WHERE stock_quantity < 0;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('negative_stock', '修复负库存', affected_count);
    
    RAISE NOTICE '修复了商品数据问题';
END $$;

-- 5. 修复订单数据
DO $$
DECLARE
    affected_count INTEGER := 0;
BEGIN
    -- 修复无效的订单状态
    UPDATE orders 
    SET order_status = 'pending' 
    WHERE order_status NOT IN ('pending', 'production', 'delivery', 'completed', 'cancelled')
    OR order_status IS NULL;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('order_status', '修复无效的订单状态', affected_count);
    
    -- 修复无效的付款状态
    UPDATE orders 
    SET payment_status = 'unpaid' 
    WHERE payment_status NOT IN ('paid', 'unpaid')
    OR payment_status IS NULL;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('payment_status', '修复无效的付款状态', affected_count);
    
    -- 修复负的订单金额
    UPDATE orders 
    SET total_price = 0 
    WHERE total_price < 0 OR total_price IS NULL;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('order_total_price', '修复无效的订单金额', affected_count);
    
    RAISE NOTICE '修复了订单数据问题';
END $$;

-- 6. 修复订单项数据
DO $$
DECLARE
    affected_count INTEGER := 0;
BEGIN
    -- 修复无效的数量
    UPDATE order_items 
    SET quantity = 1 
    WHERE quantity IS NULL OR quantity <= 0;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('order_item_quantity', '修复无效的订单项数量', affected_count);
    
    -- 修复无效的价格
    UPDATE order_items 
    SET price_at_order = 0.01 
    WHERE price_at_order IS NULL OR price_at_order < 0;
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('order_item_price', '修复无效的订单项价格', affected_count);
    
    RAISE NOTICE '修复了订单项数据问题';
END $$;

-- 7. 清理孤儿记录
DO $$
DECLARE
    affected_count INTEGER := 0;
BEGIN
    -- 删除没有对应客户的地址
    DELETE FROM addresses 
    WHERE customer_id NOT IN (SELECT id FROM customers);
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('orphan_addresses', '删除孤儿地址记录', affected_count);
    
    -- 删除没有对应客户的订单
    DELETE FROM orders 
    WHERE customer_id NOT IN (SELECT id FROM customers);
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('orphan_orders', '删除孤儿订单记录', affected_count);
    
    -- 删除没有对应订单的订单项
    DELETE FROM order_items 
    WHERE order_id NOT IN (SELECT id FROM orders);
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('orphan_order_items', '删除孤儿订单项记录', affected_count);
    
    RAISE NOTICE '清理了孤儿记录';
END $$;

-- 8. 重新计算订单总价
DO $$
DECLARE
    affected_count INTEGER := 0;
BEGIN
    UPDATE orders 
    SET total_price = (
        SELECT COALESCE(SUM(oi.quantity * oi.price_at_order), 0)
        FROM order_items oi
        WHERE oi.order_id = orders.id
    );
    
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('recalculate_order_totals', '重新计算订单总价', affected_count);
    
    RAISE NOTICE '重新计算了 % 个订单的总价', affected_count;
END $$;

-- 9. 更新统计信息
DO $$
DECLARE
    customer_count INTEGER;
    address_count INTEGER;
    product_count INTEGER;
    order_count INTEGER;
    total_fixes INTEGER;
BEGIN
    SELECT COUNT(*) INTO customer_count FROM customers;
    SELECT COUNT(*) INTO address_count FROM addresses;
    SELECT COUNT(*) INTO product_count FROM products;
    SELECT COUNT(*) INTO order_count FROM orders;
    SELECT SUM(records_affected) INTO total_fixes FROM data_fix_log WHERE executed_at >= NOW() - INTERVAL '1 hour';
    
    INSERT INTO data_fix_log (fix_type, description, records_affected)
    VALUES ('summary', 
            FORMAT('数据修复完成。客户: %s, 地址: %s, 商品: %s, 订单: %s, 总修复: %s', 
                   customer_count, address_count, product_count, order_count, COALESCE(total_fixes, 0)), 
            COALESCE(total_fixes, 0));
    
    RAISE NOTICE '数据修复完成！';
    RAISE NOTICE '客户总数: %', customer_count;
    RAISE NOTICE '地址总数: %', address_count;
    RAISE NOTICE '商品总数: %', product_count;
    RAISE NOTICE '订单总数: %', order_count;
    RAISE NOTICE '总修复记录数: %', COALESCE(total_fixes, 0);
END $$;

-- 10. 验证修复结果
DO $$
DECLARE
    validation_errors INTEGER := 0;
BEGIN
    -- 检查是否还有数据完整性问题
    SELECT COUNT(*) INTO validation_errors FROM (
        -- 检查客户名称
        SELECT 1 FROM customers WHERE name IS NULL OR name = ''
        UNION ALL
        -- 检查多个默认地址
        SELECT 1 FROM (
            SELECT customer_id 
            FROM addresses 
            WHERE is_default = true 
            GROUP BY customer_id 
            HAVING COUNT(*) > 1
        ) t
        UNION ALL
        -- 检查商品价格
        SELECT 1 FROM products WHERE price IS NULL OR price <= 0
        UNION ALL
        -- 检查订单状态
        SELECT 1 FROM orders WHERE order_status NOT IN ('pending', 'production', 'delivery', 'completed', 'cancelled')
    ) validation_check;
    
    IF validation_errors > 0 THEN
        RAISE WARNING '仍有 % 个数据完整性问题需要手动处理', validation_errors;
        INSERT INTO data_fix_log (fix_type, description, records_affected, success)
        VALUES ('validation', '数据验证发现剩余问题', validation_errors, false);
    ELSE
        RAISE NOTICE '所有数据完整性问题已修复！';
        INSERT INTO data_fix_log (fix_type, description, records_affected)
        VALUES ('validation', '数据验证通过', 0);
    END IF;
END $$;

COMMIT;

-- 显示修复日志
SELECT 
    fix_type,
    description,
    records_affected,
    executed_at,
    success
FROM data_fix_log 
WHERE executed_at >= NOW() - INTERVAL '1 hour'
ORDER BY executed_at;
