package models

import (
	"time"
)

// Holiday 节假日模型
type Holiday struct {
	ID         int64     `json:"id" xorm:"pk autoincr 'id'"`
	Date       time.Time `json:"date" xorm:"date notnull 'date'"`
	Name       string    `json:"name" xorm:"varchar(100) notnull 'name'"`
	Type       string    `json:"type" xorm:"varchar(20) notnull 'type'"` // 'holiday', 'workday', 'festival'
	IsOfficial bool      `json:"is_official" xorm:"bool notnull default(true) 'is_official'"`
	Year       int       `json:"year" xorm:"int notnull 'year'"`
	CreatedAt  time.Time `json:"created_at" xorm:"created 'created_at'"`
}

// TableName 指定表名
func (Holiday) TableName() string {
	return "holidays"
}

// HolidayRequest 节假日请求
type HolidayRequest struct {
	Date       time.Time `json:"date" binding:"required"`
	Name       string    `json:"name" binding:"required"`
	Type       string    `json:"type" binding:"required"`
	IsOfficial bool      `json:"is_official"`
}

// HolidayResponse 节假日响应
type HolidayResponse struct {
	Date       string `json:"date"`
	Name       string `json:"name"`
	Type       string `json:"type"`
	IsOfficial bool   `json:"is_official"`
}

// ToResponse 转换为响应格式
func (h *Holiday) ToResponse() *HolidayResponse {
	return &HolidayResponse{
		Date:       h.Date.Format("2006-01-02"),
		Name:       h.Name,
		Type:       h.Type,
		IsOfficial: h.IsOfficial,
	}
}

// 第三方API响应结构
type ThirdPartyHolidayAPI struct {
	Code int `json:"code"`
	Data struct {
		Year     int `json:"year"`
		Holidays []struct {
			Date   string `json:"date"`
			Name   string `json:"name"`
			IsOff  bool   `json:"isOff"`
			Type   string `json:"type"`
		} `json:"holiday"`
	} `json:"data"`
}

// LunarDate 农历日期
type LunarDate struct {
	Year     int    `json:"year"`
	Month    int    `json:"month"`
	Day      int    `json:"day"`
	MonthCn  string `json:"month_cn"`
	DayCn    string `json:"day_cn"`
	YearCn   string `json:"year_cn"`
	Festival string `json:"festival,omitempty"` // 农历节日
}

// CalendarDate 日历日期信息
type CalendarDate struct {
	Date     string     `json:"date"`
	Solar    string     `json:"solar"`
	Lunar    *LunarDate `json:"lunar"`
	Holiday  *Holiday   `json:"holiday,omitempty"`
	IsToday  bool       `json:"is_today"`
	IsWeekend bool      `json:"is_weekend"`
}
