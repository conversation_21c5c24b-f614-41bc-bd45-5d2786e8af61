<template>
  <div class="customers-view">
    <div class="page-header">
      <h2>客户管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新增客户
      </el-button>
    </div>

    <el-card>
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索客户姓名或联系方式"
          prefix-icon="Search"
          style="width: 300px"
          clearable
        />
      </div>

      <el-table :data="filteredCustomers" style="width: 100%" v-loading="customersStore.loading">
        <el-table-column prop="name" label="客户姓名" />
        <el-table-column prop="contact" label="联系方式" />
        <el-table-column label="地址信息" width="200">
          <template #default="scope">
            <div class="address-info">
              <div class="address-count">
                <el-tag type="info" size="small">
                  {{ scope.row.addresses?.length || 0 }}个地址
                </el-tag>
                <el-button
                  v-if="scope.row.addresses?.length > 0"
                  type="text"
                  size="small"
                  @click="viewCustomerDetail(scope.row)"
                >
                  查看详情
                </el-button>
              </div>
              <div v-if="getDefaultAddress(scope.row)" class="default-address">
                <span class="address-text">
                  {{ truncateAddress(getDefaultAddress(scope.row).address_details) }}
                </span>
              </div>
              <div v-else class="no-address">
                <span class="text-muted">未设置默认地址</span>
                <el-button
                  type="text"
                  size="small"
                  @click="viewCustomerDetail(scope.row)"
                >
                  添加地址
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="地址数量">
          <template #default="scope">
            <el-tag type="info">{{ scope.row.addresses?.length || 0 }}个</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #default="scope">
            <el-button
              type="success"
              size="small"
              @click="viewCustomerDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="editCustomer(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="manageAddresses(scope.row)"
            >
              地址管理
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteCustomer(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑客户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingCustomer ? '编辑客户' : '新增客户'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="customerFormRef"
        :model="customerForm"
        :rules="customerRules"
        label-width="100px"
      >
        <el-form-item label="客户姓名" prop="name">
          <el-input v-model="customerForm.name" placeholder="请输入客户姓名" />
        </el-form-item>

        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="customerForm.contact" placeholder="请输入手机号或微信号" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCustomer" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 地址管理对话框 -->
    <el-dialog
      v-model="showAddressDialog"
      title="地址管理"
      width="800px"
      @close="currentCustomer = null"
    >
      <div v-if="currentCustomer">
        <div class="address-header">
          <h4>{{ currentCustomer.name }} 的地址信息</h4>
          <el-button type="primary" size="small" @click="showAddAddressDialog = true">
            <el-icon><Plus /></el-icon>
            新增地址
          </el-button>
        </div>

        <el-table :data="sortedAddresses" style="width: 100%">
          <el-table-column prop="recipient_name" label="收货人" width="100" />
          <el-table-column prop="recipient_phone" label="联系电话" width="120" />
          <el-table-column prop="address_details" label="详细地址" min-width="200" show-overflow-tooltip />
          <el-table-column label="默认地址" width="80" align="center">
            <template #default="scope">
              <el-switch
                v-model="scope.row.is_default"
                @change="setDefaultAddress(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="添加时间" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="editAddress(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteAddress(scope.row)"
                :disabled="scope.row.is_default"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="!currentCustomer.addresses || currentCustomer.addresses.length === 0" class="empty-addresses">
          <el-empty description="暂无地址信息">
            <el-button type="primary" @click="showAddAddressDialog = true">添加第一个地址</el-button>
          </el-empty>
        </div>
      </div>
    </el-dialog>

    <!-- 新增/编辑地址对话框 -->
    <el-dialog
      v-model="showAddAddressDialog"
      :title="editingAddress ? '编辑地址' : '新增地址'"
      width="600px"
      @close="resetAddressForm"
    >
      <el-form
        ref="addressFormRef"
        :model="addressForm"
        :rules="addressRules"
        label-width="100px"
      >
        <el-form-item label="收货人" prop="recipient_name">
          <el-input
            v-model="addressForm.recipient_name"
            placeholder="请输入收货人姓名"
            clearable
          />
          <div class="form-tip">
            <el-button
              type="text"
              size="small"
              @click="fillCustomerInfo"
              v-if="currentCustomer"
            >
              使用客户信息
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="联系电话" prop="recipient_phone">
          <el-input
            v-model="addressForm.recipient_phone"
            placeholder="请输入联系电话"
            clearable
          />
        </el-form-item>

        <el-form-item label="详细地址" prop="address_details">
          <el-input
            v-model="addressForm.address_details"
            type="textarea"
            :rows="3"
            placeholder="请输入详细地址，如：省市区街道门牌号"
            show-word-limit
            maxlength="200"
          />
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="addressForm.is_default" />
          <span class="form-tip">设为默认后，新订单将优先使用此地址</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddAddressDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAddress" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCustomersStore } from '../stores/customers'

const router = useRouter()

const customersStore = useCustomersStore()

// 状态
const searchQuery = ref('')
const showAddDialog = ref(false)
const showAddressDialog = ref(false)
const showAddAddressDialog = ref(false)
const editingCustomer = ref(null)
const editingAddress = ref(null)
const currentCustomer = ref(null)
const saving = ref(false)

// 表单数据
const customerForm = reactive({
  name: '',
  contact: ''
})

const addressForm = reactive({
  recipient_name: '',
  recipient_phone: '',
  address_details: '',
  is_default: false
})

// 表单验证规则
const customerRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ]
}

const addressRules = {
  recipient_name: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' }
  ],
  recipient_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  address_details: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ]
}

const customerFormRef = ref()
const addressFormRef = ref()

// 计算属性
const filteredCustomers = computed(() => {
  return customersStore.searchCustomers(searchQuery.value)
})

// 排序后的地址列表（默认地址在前，然后按创建时间倒序）
const sortedAddresses = computed(() => {
  if (!currentCustomer.value?.addresses) return []

  return [...currentCustomer.value.addresses].sort((a, b) => {
    // 默认地址优先
    if (a.is_default && !b.is_default) return -1
    if (!a.is_default && b.is_default) return 1

    // 按创建时间倒序
    const dateA = new Date(a.created_at || 0)
    const dateB = new Date(b.created_at || 0)
    return dateB - dateA
  })
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取默认地址
const getDefaultAddress = (customer) => {
  return customer.addresses?.find(addr => addr.is_default)
}

// 查看客户详情
const viewCustomerDetail = (customer) => {
  router.push(`/customers/${customer.id}`)
}

// 截断地址显示
const truncateAddress = (address) => {
  if (!address) return ''
  return address.length > 30 ? address.substring(0, 30) + '...' : address
}

// 编辑客户
const editCustomer = (customer) => {
  editingCustomer.value = customer
  Object.assign(customerForm, {
    name: customer.name,
    contact: customer.contact
  })
  showAddDialog.value = true
}

// 保存客户
const saveCustomer = async () => {
  if (!customerFormRef.value) return

  await customerFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saving.value = true

        let result
        if (editingCustomer.value) {
          result = await customersStore.updateCustomer(editingCustomer.value.id, customerForm)
        } else {
          result = await customersStore.createCustomer(customerForm)
        }

        if (result.success) {
          showAddDialog.value = false
          resetForm()
        }
      } catch (error) {
        console.error('保存客户失败:', error)
      } finally {
        saving.value = false
      }
    }
  })
}

// 删除客户
const deleteCustomer = async (customer) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户"${customer.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await customersStore.deleteCustomer(customer.id)
  } catch (error) {
    if (error.message) {
      console.error('删除客户失败:', error)
    }
  }
}

// 管理地址
const manageAddresses = (customer) => {
  currentCustomer.value = customer
  showAddressDialog.value = true
}

// 编辑地址
const editAddress = (address) => {
  editingAddress.value = address
  Object.assign(addressForm, {
    recipient_name: address.recipient_name,
    recipient_phone: address.recipient_phone,
    address_details: address.address_details,
    is_default: address.is_default
  })
  showAddAddressDialog.value = true
}

// 保存地址
const saveAddress = async () => {
  if (!addressFormRef.value) return

  await addressFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saving.value = true

        let result
        if (editingAddress.value) {
          result = await customersStore.updateAddress(editingAddress.value.id, addressForm)
        } else {
          result = await customersStore.addAddress(currentCustomer.value.id, addressForm)
        }

        if (result.success) {
          showAddAddressDialog.value = false
          resetAddressForm()
        }
      } catch (error) {
        console.error('保存地址失败:', error)
      } finally {
        saving.value = false
      }
    }
  })
}

// 删除地址
const deleteAddress = async (address) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个地址吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await customersStore.deleteAddress(address.id)
  } catch (error) {
    if (error.message) {
      console.error('删除地址失败:', error)
    }
  }
}

// 设置默认地址
const setDefaultAddress = async (address) => {
  if (address.is_default) {
    await customersStore.setDefaultAddress(currentCustomer.value.id, address.id)
  }
}

// 填充客户信息到地址表单
const fillCustomerInfo = () => {
  if (currentCustomer.value) {
    addressForm.recipient_name = currentCustomer.value.name
    addressForm.recipient_phone = currentCustomer.value.contact
  }
}

// 重置表单
const resetForm = () => {
  editingCustomer.value = null
  Object.assign(customerForm, {
    name: '',
    contact: ''
  })
}

const resetAddressForm = () => {
  editingAddress.value = null
  Object.assign(addressForm, {
    recipient_name: '',
    recipient_phone: '',
    address_details: '',
    is_default: false
  })
}

// 组件挂载时获取数据
onMounted(() => {
  customersStore.fetchCustomers()
})
</script>

<style scoped>
.customers-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.search-bar {
  margin-bottom: 20px;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.address-header h4 {
  margin: 0;
}

.text-muted {
  color: #909399;
}

.address-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.address-count {
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-address {
  font-size: 12px;
}

.address-text {
  color: #606266;
  line-height: 1.4;
}

.no-address {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.empty-addresses {
  padding: 40px 0;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .customers-view {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .search-bar {
    margin-bottom: 15px;
  }

  .search-bar .el-input {
    width: 100% !important;
  }

  .address-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .el-table {
    font-size: 14px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}
</style>
