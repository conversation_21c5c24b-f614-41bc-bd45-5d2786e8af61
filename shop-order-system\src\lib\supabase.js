import { createClient } from '@supabase/supabase-js'

// 从环境变量中获取Supabase配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// 检查环境变量是否配置
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase配置缺失！请检查环境变量：')
  console.error('VITE_SUPABASE_URL:', supabaseUrl)
  console.error('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? '已配置' : '未配置')

  // 在开发环境下提供友好的错误提示
  if (import.meta.env.DEV) {
    alert(`
Supabase配置缺失！

请按以下步骤配置：
1. 复制 .env.example 为 .env
2. 在 .env 文件中填写您的Supabase项目信息
3. 重启开发服务器

或者您可以先使用演示模式（仅前端界面）
    `)
  }
}

// 创建Supabase客户端，如果配置缺失则使用占位符
export const supabase = createClient(
  supabaseUrl || 'https://placeholder.supabase.co',
  supabaseAnonKey || 'placeholder-key'
)

// 导出配置状态
export const isSupabaseConfigured = !!(supabaseUrl && supabaseAnonKey)
