package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID        int64     `json:"id" xorm:"pk autoincr 'id'"`
	Email     string    `json:"email" xorm:"varchar(255) notnull unique 'email'"`
	Password  string    `json:"-" xorm:"varchar(255) notnull 'password'"` // 不在JSON中返回密码
	Role      string    `json:"role" xorm:"varchar(50) notnull default('staff') 'role'"`
	IsActive  bool      `json:"is_active" xorm:"bool notnull default(true) 'is_active'"`
	CreatedAt time.Time `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt time.Time `json:"updated_at" xorm:"updated 'updated_at'"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == "super_admin"
}

// UserResponse 用户响应结构（不包含密码）
type UserResponse struct {
	ID        int64     `json:"id"`
	Email     string    `json:"email"`
	Role      string    `json:"role"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ToResponse 转换为响应结构
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:        u.ID,
		Email:     u.Email,
		Role:      u.Role,
		IsActive:  u.IsActive,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Role     string `json:"role,omitempty"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token string        `json:"token"`
	User  *UserResponse `json:"user"`
}
