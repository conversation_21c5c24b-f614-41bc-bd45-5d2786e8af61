<template>
  <div class="file-manager">
    <el-dialog
      v-model="visible"
      title="文件管理"
      width="80%"
      :before-close="handleClose"
    >
      <div class="file-manager-content">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="showUploadDialog = true" :icon="Upload">
              上传文件
            </el-button>
            <el-button @click="refreshFiles" :icon="Refresh" :loading="loading">
              刷新
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchQuery"
              placeholder="搜索文件名"
              style="width: 200px;"
              clearable
              :prefix-icon="Search"
            />
          </div>
        </div>

        <!-- 文件网格 -->
        <div class="file-grid" v-loading="loading">
          <div
            v-for="file in filteredFiles"
            :key="file.filename"
            class="file-item"
            :class="{ 'selected': selectedFile?.filename === file.filename }"
            @click="selectFile(file)"
            @dblclick="confirmSelect(file)"
          >
            <div class="file-preview">
              <img
                :src="file.url"
                :alt="file.filename"
                @error="handleImageError"
                @load="handleImageLoad"
              />
            </div>
            <div class="file-info">
              <div class="file-name" :title="file.filename">
                {{ truncateFilename(file.filename) }}
              </div>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span class="file-date">{{ formatDate(file.modified_at) }}</span>
              </div>
            </div>
            <div class="file-actions">
              <el-button
                type="text"
                size="small"
                @click.stop="previewFile(file)"
                :icon="View"
              />
              <el-button
                type="text"
                size="small"
                @click.stop="copyUrl(file)"
                :icon="CopyDocument"
              />
              <el-button
                type="text"
                size="small"
                @click.stop="deleteFile(file)"
                :icon="Delete"
                class="delete-btn"
              />
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && filteredFiles.length === 0" class="empty-state">
          <el-empty description="暂无文件">
            <el-button type="primary" @click="showUploadDialog = true">
              上传第一个文件
            </el-button>
          </el-empty>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="selected-info">
            <span v-if="selectedFile">
              已选择: {{ selectedFile.filename }}
            </span>
          </div>
          <div class="dialog-actions">
            <el-button @click="handleClose">取消</el-button>
            <el-button
              type="primary"
              @click="confirmSelect(selectedFile)"
              :disabled="!selectedFile"
            >
              确定选择
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文件"
      width="500px"
    >
      <el-upload
        ref="uploadRef"
        class="upload-area"
        :http-request="handleUpload"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        multiple
        drag
        accept="image/*"
      >
        <el-icon class="upload-icon"><Upload /></el-icon>
        <div class="upload-text">
          <p>点击或拖拽文件到此处上传</p>
          <p class="upload-tip">支持批量上传，单个文件不超过5MB</p>
        </div>
      </el-upload>
      
      <template #footer>
        <el-button @click="showUploadDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="previewFileData?.filename"
      width="60%"
      center
    >
      <div class="preview-container">
        <img
          v-if="previewFileData"
          :src="previewFileData.url"
          :alt="previewFileData.filename"
          style="max-width: 100%; max-height: 70vh;"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  Refresh,
  Search,
  View,
  CopyDocument,
  Delete
} from '@element-plus/icons-vue'
import { productAPI } from '../lib/api'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  category: {
    type: String,
    default: 'images'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'file-selected'])

// 状态
const files = ref([])
const loading = ref(false)
const searchQuery = ref('')
const selectedFile = ref(null)
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const previewFileData = ref(null)
const uploadRef = ref()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const filteredFiles = computed(() => {
  if (!searchQuery.value) return files.value
  
  const query = searchQuery.value.toLowerCase()
  return files.value.filter(file =>
    file.filename.toLowerCase().includes(query)
  )
})

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadFiles()
  }
})

// 方法
const loadFiles = async () => {
  try {
    loading.value = true
    const result = await productAPI.getFileList(props.category)
    
    if (result.success) {
      files.value = result.files || []
    }
  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  } finally {
    loading.value = false
  }
}

const refreshFiles = () => {
  loadFiles()
}

const selectFile = (file) => {
  selectedFile.value = file
}

const confirmSelect = (file) => {
  if (file) {
    emit('file-selected', file)
    handleClose()
  }
}

const handleClose = () => {
  visible.value = false
  selectedFile.value = null
  searchQuery.value = ''
}

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleUpload = async (options) => {
  const { file } = options
  
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    const result = await productAPI.uploadImage(formData)
    
    if (result.success) {
      ElMessage.success(`${file.name} 上传成功`)
      refreshFiles()
    } else {
      throw new Error(result.error || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error(`${file.name} 上传失败`)
  }
}

const handleUploadSuccess = () => {
  refreshFiles()
}

const handleUploadError = (error) => {
  console.error('上传错误:', error)
}

const previewFile = (file) => {
  previewFileData.value = file
  showPreviewDialog.value = true
}

const copyUrl = async (file) => {
  try {
    await navigator.clipboard.writeText(file.url)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const deleteFile = async (file) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.filename}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const result = await productAPI.deleteImage(file.filename, file.path)
    if (result.success) {
      ElMessage.success('文件删除成功')
      refreshFiles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

const truncateFilename = (filename) => {
  if (filename.length <= 20) return filename
  const ext = filename.split('.').pop()
  const name = filename.substring(0, filename.lastIndexOf('.'))
  return `${name.substring(0, 15)}...${ext}`
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.png'
}

const handleImageLoad = (event) => {
  // 图片加载成功的处理
}

// 组件挂载时加载文件
onMounted(() => {
  if (visible.value) {
    loadFiles()
  }
})
</script>

<style scoped>
.file-manager-content {
  min-height: 400px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  min-height: 300px;
}

.file-item {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.file-item:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-item.selected {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.file-preview {
  width: 100%;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.file-info {
  margin-bottom: 8px;
}

.file-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  word-break: break-all;
}

.file-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.file-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.file-item:hover .file-actions {
  opacity: 1;
}

.delete-btn {
  color: var(--el-color-danger);
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-info {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.upload-area {
  width: 100%;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-text p {
  margin: 0;
  color: #606266;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.preview-container {
  text-align: center;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}
</style>
