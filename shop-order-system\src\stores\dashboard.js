import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useOrdersStore } from './orders'
import { useCustomersStore } from './customers'
import { useProductsStore } from './products'

export const useDashboardStore = defineStore('dashboard', () => {
  const ordersStore = useOrdersStore()
  const customersStore = useCustomersStore()
  const productsStore = useProductsStore()

  // 状态
  const dateRange = ref('today') // today, week, month
  const loading = ref(false)

  // 计算属性 - 基础统计
  const totalSales = computed(() => {
    const paidOrders = ordersStore.orders.filter(order => order.payment_status === 'paid')
    return paidOrders.reduce((sum, order) => sum + parseFloat(order.total_price || 0), 0)
  })

  const totalOrders = computed(() => ordersStore.orders.length)

  const pendingOrders = computed(() => 
    ordersStore.orders.filter(order => 
      order.order_status === 'pending' || 
      order.order_status === 'production' || 
      order.order_status === 'delivery'
    ).length
  )

  const newCustomers = computed(() => {
    const today = new Date()
    const timeRange = getTimeRange(dateRange.value)
    
    return customersStore.customers.filter(customer => {
      const createdDate = new Date(customer.created_at)
      return createdDate >= timeRange.start && createdDate <= timeRange.end
    }).length
  })

  // 销售趋势数据
  const salesTrendData = computed(() => {
    const days = getLast30Days()
    const salesByDay = {}
    
    // 初始化每天的销售额为0
    days.forEach(day => {
      salesByDay[day] = 0
    })
    
    // 计算每天的销售额
    ordersStore.orders.forEach(order => {
      if (order.payment_status === 'paid' && order.created_at) {
        const orderDate = new Date(order.created_at).toISOString().split('T')[0]
        if (salesByDay.hasOwnProperty(orderDate)) {
          salesByDay[orderDate] += parseFloat(order.total_price || 0)
        }
      }
    })
    
    return {
      dates: days,
      sales: days.map(day => salesByDay[day])
    }
  })

  // 热门商品数据
  const popularProductsData = computed(() => {
    const productSales = {}
    
    ordersStore.orders.forEach(order => {
      if (order.order_items) {
        order.order_items.forEach(item => {
          const productName = item.products?.name || '未知商品'
          if (!productSales[productName]) {
            productSales[productName] = 0
          }
          productSales[productName] += parseInt(item.quantity || 0)
        })
      }
    })
    
    // 排序并取前5名
    const sorted = Object.entries(productSales)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
    
    return {
      products: sorted.map(([name]) => name),
      quantities: sorted.map(([, quantity]) => quantity)
    }
  })

  // 订单状态分布数据
  const orderStatusData = computed(() => {
    const statusCount = {
      pending: 0,
      production: 0,
      delivery: 0,
      completed: 0,
      cancelled: 0
    }
    
    ordersStore.orders.forEach(order => {
      const status = order.order_status || 'pending'
      if (statusCount.hasOwnProperty(status)) {
        statusCount[status]++
      }
    })
    
    return Object.entries(statusCount).map(([status, count]) => ({
      name: getStatusText(status),
      value: count
    }))
  })

  // 付款状态分布数据
  const paymentStatusData = computed(() => {
    const paymentCount = { paid: 0, unpaid: 0 }
    
    ordersStore.orders.forEach(order => {
      const status = order.payment_status || 'unpaid'
      if (paymentCount.hasOwnProperty(status)) {
        paymentCount[status]++
      }
    })
    
    return [
      { name: '已付款', value: paymentCount.paid },
      { name: '未付款', value: paymentCount.unpaid }
    ]
  })

  // 最近订单数据
  const recentOrders = computed(() => {
    return ordersStore.orders
      .slice(0, 5)
      .map(order => ({
        id: order.id,
        customer: order.customers?.name || '未知客户',
        amount: `¥${parseFloat(order.total_price || 0).toFixed(2)}`,
        status: getStatusText(order.order_status),
        statusType: getStatusType(order.order_status)
      }))
  })

  // 辅助函数
  const getTimeRange = (range) => {
    const today = new Date()
    const start = new Date(today)
    
    switch (range) {
      case 'today':
        start.setHours(0, 0, 0, 0)
        break
      case 'week':
        start.setDate(today.getDate() - 7)
        break
      case 'month':
        start.setMonth(today.getMonth() - 1)
        break
    }
    
    return { start, end: today }
  }

  const getLast30Days = () => {
    const days = []
    const today = new Date()
    
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      days.push(date.toISOString().split('T')[0])
    }
    
    return days
  }

  const getStatusText = (status) => {
    const statusMap = {
      'pending': '待处理',
      'production': '生产中',
      'delivery': '配送中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || status
  }

  const getStatusType = (status) => {
    const statusMap = {
      'pending': 'danger',
      'production': 'info',
      'delivery': 'warning',
      'completed': 'success',
      'cancelled': ''
    }
    return statusMap[status] || 'info'
  }

  // 刷新数据
  const refreshData = async () => {
    try {
      loading.value = true
      await Promise.all([
        ordersStore.fetchOrders(),
        customersStore.fetchCustomers(),
        productsStore.fetchProducts()
      ])
    } catch (error) {
      console.error('刷新仪表盘数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    dateRange,
    loading,
    
    // 计算属性
    totalSales,
    totalOrders,
    pendingOrders,
    newCustomers,
    salesTrendData,
    popularProductsData,
    orderStatusData,
    paymentStatusData,
    recentOrders,
    
    // 方法
    refreshData
  }
})
