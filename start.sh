#!/bin/bash

# 小商店订单管理系统启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_success() {
    print_message "$1" "$GREEN"
}

print_error() {
    print_message "$1" "$RED"
}

print_warning() {
    print_message "$1" "$YELLOW"
}

print_info() {
    print_message "$1" "$BLUE"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 检查端口是否被占用
check_ports() {
    local ports=("3000" "8080")
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_warning "端口 $port 已被占用，请确保没有冲突"
        fi
    done
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    mkdir -p backend/data
    mkdir -p backend/uploads
}

# 复制环境变量文件
setup_env_files() {
    print_info "设置环境变量文件..."
    
    # 后端环境变量
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_success "已创建 backend/.env 文件"
    else
        print_info "backend/.env 文件已存在"
    fi
    
    # 前端环境变量
    if [ ! -f "shop-order-system/.env" ]; then
        cat > shop-order-system/.env << EOF
VITE_API_BASE_URL=http://localhost:8080/api
EOF
        print_success "已创建 shop-order-system/.env 文件"
    else
        print_info "shop-order-system/.env 文件已存在"
    fi
}

# 启动服务
start_services() {
    local mode=$1
    
    print_info "启动服务 (模式: $mode)..."
    
    case $mode in
        "sqlite")
            docker-compose up -d
            ;;
        "mysql")
            docker-compose --profile mysql up -d
            ;;
        *)
            print_error "未知模式: $mode"
            exit 1
            ;;
    esac
}

# 等待服务启动
wait_for_services() {
    print_info "等待服务启动..."
    
    # 等待后端服务
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:8080/health > /dev/null 2>&1; then
            print_success "后端服务已启动"
            break
        fi
        
        attempt=$((attempt + 1))
        print_info "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 2
    done
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "后端服务启动超时"
        exit 1
    fi
    
    # 等待前端服务
    attempt=0
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            print_success "前端服务已启动"
            break
        fi
        
        attempt=$((attempt + 1))
        print_info "等待前端服务启动... ($attempt/$max_attempts)"
        sleep 2
    done
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "前端服务启动超时"
        exit 1
    fi
}

# 显示服务信息
show_service_info() {
    print_success "🎉 服务启动成功！"
    echo
    print_info "服务地址："
    echo "  前端应用: http://localhost:3000"
    echo "  后端API:  http://localhost:8080"
    echo "  健康检查: http://localhost:8080/health"
    echo
    print_info "默认账户："
    echo "  管理员: <EMAIL> / demo123"
    echo "  员工:   <EMAIL> / demo123"
    echo
    print_info "常用命令："
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
}

# 停止服务
stop_services() {
    print_info "停止服务..."
    docker-compose down
    print_success "服务已停止"
}

# 查看日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$service"
    fi
}

# 显示帮助信息
show_help() {
    echo "小商店订单管理系统启动脚本"
    echo
    echo "用法: $0 [命令] [选项]"
    echo
    echo "命令:"
    echo "  start [sqlite|mysql]  启动服务 (默认: sqlite)"
    echo "  stop                  停止服务"
    echo "  restart [sqlite|mysql] 重启服务"
    echo "  logs [service]        查看日志"
    echo "  status                查看服务状态"
    echo "  help                  显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 start              # 使用 SQLite 启动"
    echo "  $0 start mysql        # 使用 MySQL 启动"
    echo "  $0 logs backend       # 查看后端日志"
    echo "  $0 stop               # 停止所有服务"
}

# 查看服务状态
show_status() {
    print_info "服务状态："
    docker-compose ps
}

# 主函数
main() {
    local command=${1:-"start"}
    local option=${2:-"sqlite"}
    
    case $command in
        "start")
            check_docker
            check_ports
            create_directories
            setup_env_files
            start_services "$option"
            wait_for_services
            show_service_info
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 2
            start_services "$option"
            wait_for_services
            show_service_info
            ;;
        "logs")
            show_logs "$option"
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
