// 演示数据 - 当Supabase未配置时使用
export const demoProducts = [
  {
    id: 1,
    name: '巧克力生日蛋糕',
    category: '蛋糕',
    price: 128.00,
    is_listed: true,
    image_url: '',
    custom_attributes: {
      "规格": ["6寸", "8寸", "10寸"],
      "口味": ["巧克力", "香草"]
    },
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: '草莓奶油蛋糕',
    category: '蛋糕',
    price: 138.00,
    is_listed: true,
    image_url: '',
    custom_attributes: {
      "规格": ["6寸", "8寸", "10寸"],
      "口味": ["草莓", "香草"]
    },
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    name: '曲奇饼干礼盒',
    category: '饼干',
    price: 68.00,
    is_listed: true,
    image_url: '',
    custom_attributes: {
      "包装": ["小盒", "大盒"],
      "口味": ["原味", "巧克力", "抹茶"]
    },
    created_at: '2024-01-03T00:00:00Z'
  }
]

export const demoCustomers = [
  {
    id: 1,
    name: '张三',
    contact: '13800138001',
    created_at: '2024-01-01T00:00:00Z',
    addresses: [
      {
        id: 1,
        customer_id: 1,
        recipient_name: '张三',
        recipient_phone: '13800138001',
        address_details: '北京市朝阳区建国路88号SOHO现代城A座1001',
        is_default: true,
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 11,
        customer_id: 1,
        recipient_name: '张三',
        recipient_phone: '13800138001',
        address_details: '北京市海淀区中关村大街1号鼎好大厦B座808',
        is_default: false,
        created_at: '2023-12-15T00:00:00Z'
      },
      {
        id: 12,
        customer_id: 1,
        recipient_name: '张小三',
        recipient_phone: '13800138011',
        address_details: '北京市西城区金融街35号国际企业大厦C座1205',
        is_default: false,
        created_at: '2023-11-20T00:00:00Z'
      }
    ]
  },
  {
    id: 2,
    name: '李四',
    contact: '13800138002',
    created_at: '2024-01-02T00:00:00Z',
    addresses: [
      {
        id: 2,
        customer_id: 2,
        recipient_name: '李四',
        recipient_phone: '13800138002',
        address_details: '上海市浦东新区陆家嘴环路1000号',
        is_default: true,
        created_at: '2024-01-02T00:00:00Z'
      },
      {
        id: 21,
        customer_id: 2,
        recipient_name: '李四',
        recipient_phone: '13800138002',
        address_details: '上海市黄浦区南京东路步行街99号',
        is_default: false,
        created_at: '2023-10-10T00:00:00Z'
      },
      {
        id: 22,
        customer_id: 2,
        recipient_name: '李小四',
        recipient_phone: '13800138022',
        address_details: '上海市徐汇区淮海中路1045号淮海国际广场',
        is_default: false,
        created_at: '2023-09-05T00:00:00Z'
      }
    ]
  },
  {
    id: 3,
    name: '王五',
    contact: '13800138003',
    created_at: '2024-01-03T00:00:00Z',
    addresses: [
      {
        id: 3,
        customer_id: 3,
        recipient_name: '王五',
        recipient_phone: '13800138003',
        address_details: '广州市天河区珠江新城花城大道85号',
        is_default: true,
        created_at: '2024-01-03T00:00:00Z'
      },
      {
        id: 31,
        customer_id: 3,
        recipient_name: '王五',
        recipient_phone: '13800138003',
        address_details: '广州市越秀区北京路步行街168号',
        is_default: false,
        created_at: '2023-12-01T00:00:00Z'
      },
      {
        id: 32,
        customer_id: 3,
        recipient_name: '王小五',
        recipient_phone: '13800138033',
        address_details: '深圳市南山区科技园南区深南大道9988号',
        is_default: false,
        created_at: '2023-08-15T00:00:00Z'
      },
      {
        id: 33,
        customer_id: 3,
        recipient_name: '王五',
        recipient_phone: '13800138003',
        address_details: '广州市番禺区万博商务区万博二路228号',
        is_default: false,
        created_at: '2023-07-20T00:00:00Z'
      }
    ]
  }
]

export const demoOrders = [
  {
    id: 1,
    customer_id: 1,
    delivery_address: '北京市朝阳区建国路88号SOHO现代城A座1001',
    delivery_datetime: '2024-06-20T14:00:00+08:00',
    total_price: 256.00,
    payment_status: 'paid',
    order_status: 'completed',
    notes: '生日蛋糕，请写上"生日快乐"',
    created_at: '2024-06-18T10:00:00Z',
    customers: {
      id: 1,
      name: '张三',
      contact: '13800138001'
    },
    order_items: [
      {
        id: 1,
        order_id: 1,
        product_id: 1,
        quantity: 2,
        price_at_order: 128.00,
        selected_attributes: {
          "规格": "8寸",
          "口味": "巧克力"
        },
        products: {
          id: 1,
          name: '巧克力生日蛋糕',
          category: '蛋糕',
          price: 128.00
        }
      }
    ]
  },
  {
    id: 2,
    customer_id: 2,
    delivery_address: '上海市浦东新区陆家嘴环路1000号',
    delivery_datetime: '2024-06-21T16:30:00+08:00',
    total_price: 138.00,
    payment_status: 'paid',
    order_status: 'delivery',
    notes: '',
    created_at: '2024-06-19T09:00:00Z',
    customers: {
      id: 2,
      name: '李四',
      contact: '13800138002'
    },
    order_items: [
      {
        id: 2,
        order_id: 2,
        product_id: 2,
        quantity: 1,
        price_at_order: 138.00,
        selected_attributes: {
          "规格": "8寸",
          "口味": "草莓"
        },
        products: {
          id: 2,
          name: '草莓奶油蛋糕',
          category: '蛋糕',
          price: 138.00
        }
      }
    ]
  },
  {
    id: 3,
    customer_id: 3,
    delivery_address: '广州市天河区珠江新城花城大道85号',
    delivery_datetime: '2024-06-22T10:00:00+08:00',
    total_price: 68.00,
    payment_status: 'unpaid',
    order_status: 'pending',
    notes: '需要精美包装',
    created_at: '2024-06-20T14:00:00Z',
    customers: {
      id: 3,
      name: '王五',
      contact: '13800138003'
    },
    order_items: [
      {
        id: 3,
        order_id: 3,
        product_id: 3,
        quantity: 1,
        price_at_order: 68.00,
        selected_attributes: {
          "包装": "大盒",
          "口味": "混合"
        },
        products: {
          id: 3,
          name: '曲奇饼干礼盒',
          category: '饼干',
          price: 68.00
        }
      }
    ]
  }
]

export const demoUsers = [
  {
    id: 'demo-admin',
    email: '<EMAIL>',
    role: 'super_admin',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'demo-staff',
    email: '<EMAIL>',
    role: 'staff',
    is_active: true,
    created_at: '2024-01-02T00:00:00Z'
  }
]
