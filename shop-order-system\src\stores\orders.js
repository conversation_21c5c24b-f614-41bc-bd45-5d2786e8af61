import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, isSupabaseConfigured } from '../lib/supabase'
import { ElMessage } from 'element-plus'
import { demoOrders } from '../lib/demo-data'

export const useOrdersStore = defineStore('orders', () => {
  // 状态
  const orders = ref([])
  const loading = ref(false)

  // 计算属性
  const ordersByStatus = computed(() => {
    const grouped = {}
    orders.value.forEach(order => {
      const status = order.order_status || 'pending'
      if (!grouped[status]) grouped[status] = []
      grouped[status].push(order)
    })
    return grouped
  })

  const pendingOrders = computed(() => 
    orders.value.filter(order => order.order_status === 'pending')
  )

  const completedOrders = computed(() => 
    orders.value.filter(order => order.order_status === 'completed')
  )

  // 获取订单列表（包含客户和商品信息）
  const fetchOrders = async () => {
    try {
      loading.value = true

      // 如果Supabase未配置，使用演示数据
      if (!isSupabaseConfigured) {
        orders.value = [...demoOrders]
        return { success: true, data: orders.value }
      }

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          customers (
            id,
            name,
            contact
          ),
          order_items (
            *,
            products (
              id,
              name,
              category,
              price
            )
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      orders.value = data || []
      return { success: true, data }

    } catch (error) {
      console.error('获取订单列表失败:', error)
      ElMessage.error('获取订单列表失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 创建订单
  const createOrder = async (orderData, orderItems) => {
    try {
      loading.value = true
      
      // 开始事务
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert([orderData])
        .select()
        .single()

      if (orderError) {
        throw orderError
      }

      // 插入订单商品
      const itemsWithOrderId = orderItems.map(item => ({
        ...item,
        order_id: order.id
      }))

      const { data: items, error: itemsError } = await supabase
        .from('order_items')
        .insert(itemsWithOrderId)
        .select(`
          *,
          products (
            id,
            name,
            category,
            price
          )
        `)

      if (itemsError) {
        throw itemsError
      }

      // 构建完整的订单对象
      const completeOrder = {
        ...order,
        order_items: items,
        customers: null // 需要单独获取客户信息
      }

      orders.value.unshift(completeOrder)
      ElMessage.success('订单创建成功')
      return { success: true, data: completeOrder }
      
    } catch (error) {
      console.error('创建订单失败:', error)
      ElMessage.error('创建订单失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 更新订单
  const updateOrder = async (id, updates) => {
    try {
      loading.value = true
      
      const { data, error } = await supabase
        .from('orders')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        throw error
      }

      const index = orders.value.findIndex(o => o.id === id)
      if (index > -1) {
        // 保留关联数据
        const existingOrder = orders.value[index]
        orders.value[index] = {
          ...data,
          customers: existingOrder.customers,
          order_items: existingOrder.order_items
        }
      }

      ElMessage.success('订单更新成功')
      return { success: true, data }
      
    } catch (error) {
      console.error('更新订单失败:', error)
      ElMessage.error('更新订单失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 删除订单
  const deleteOrder = async (id) => {
    try {
      loading.value = true
      
      const { error } = await supabase
        .from('orders')
        .delete()
        .eq('id', id)

      if (error) {
        throw error
      }

      const index = orders.value.findIndex(o => o.id === id)
      if (index > -1) {
        orders.value.splice(index, 1)
      }

      ElMessage.success('订单删除成功')
      return { success: true }
      
    } catch (error) {
      console.error('删除订单失败:', error)
      ElMessage.error('删除订单失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 更新订单状态
  const updateOrderStatus = async (id, status) => {
    return await updateOrder(id, { order_status: status })
  }

  // 更新付款状态
  const updatePaymentStatus = async (id, status) => {
    return await updateOrder(id, { payment_status: status })
  }

  // 搜索订单
  const searchOrders = (query, filters = {}) => {
    let filtered = orders.value

    // 文本搜索
    if (query) {
      const searchTerm = query.toLowerCase()
      filtered = filtered.filter(order => 
        order.id.toString().includes(searchTerm) ||
        (order.customers?.name && order.customers.name.toLowerCase().includes(searchTerm)) ||
        (order.customers?.contact && order.customers.contact.toLowerCase().includes(searchTerm))
      )
    }

    // 状态筛选
    if (filters.orderStatus) {
      filtered = filtered.filter(order => order.order_status === filters.orderStatus)
    }

    if (filters.paymentStatus) {
      filtered = filtered.filter(order => order.payment_status === filters.paymentStatus)
    }

    // 日期范围筛选
    if (filters.dateRange && filters.dateRange.length === 2) {
      const [startDate, endDate] = filters.dateRange
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.delivery_datetime || order.created_at)
        return orderDate >= startDate && orderDate <= endDate
      })
    }

    return filtered
  }

  // 根据ID获取订单
  const getOrderById = (id) => {
    return orders.value.find(o => o.id === id)
  }

  // 计算订单总金额
  const calculateOrderTotal = (orderItems) => {
    return orderItems.reduce((total, item) => {
      return total + (item.quantity * item.price_at_order)
    }, 0)
  }

  // 获取订单统计数据
  const getOrderStats = () => {
    const stats = {
      total: orders.value.length,
      pending: 0,
      production: 0,
      delivery: 0,
      completed: 0,
      cancelled: 0,
      paid: 0,
      unpaid: 0,
      totalRevenue: 0
    }

    orders.value.forEach(order => {
      // 订单状态统计
      if (order.order_status) {
        stats[order.order_status] = (stats[order.order_status] || 0) + 1
      }

      // 付款状态统计
      if (order.payment_status === 'paid') {
        stats.paid++
        stats.totalRevenue += parseFloat(order.total_price || 0)
      } else {
        stats.unpaid++
      }
    })

    return stats
  }

  return {
    // 状态
    orders,
    loading,
    
    // 计算属性
    ordersByStatus,
    pendingOrders,
    completedOrders,
    
    // 方法
    fetchOrders,
    createOrder,
    updateOrder,
    deleteOrder,
    updateOrderStatus,
    updatePaymentStatus,
    searchOrders,
    getOrderById,
    calculateOrderTotal,
    getOrderStats
  }
})
