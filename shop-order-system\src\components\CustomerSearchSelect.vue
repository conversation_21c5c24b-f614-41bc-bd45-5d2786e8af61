<template>
  <div class="customer-search-select">
    <el-select
      v-model="selectedCustomer"
      filterable
      remote
      reserve-keyword
      placeholder="搜索客户（姓名/手机号）"
      :remote-method="searchCustomers"
      :loading="loading"
      :clearable="clearable"
      :disabled="disabled"
      style="width: 100%"
      @change="handleChange"
      @clear="handleClear"
    >
      <el-option
        v-for="customer in customerOptions"
        :key="customer.id"
        :label="formatCustomerLabel(customer)"
        :value="customer.id"
      >
        <div class="customer-option">
          <div class="customer-main">
            <span class="customer-name">{{ customer.name }}</span>
            <span class="customer-contact">{{ customer.contact }}</span>
          </div>
          <div class="customer-meta" v-if="customer.address_count">
            <el-tag size="small" type="info">{{ customer.address_count }}个地址</el-tag>
          </div>
        </div>
      </el-option>
      
      <!-- 无搜索结果时显示 -->
      <el-option
        v-if="searchQuery && customerOptions.length === 0 && !loading"
        disabled
        value=""
        label="无匹配客户"
      />
      
      <!-- 快速添加客户选项 -->
      <el-option
        v-if="showAddOption && searchQuery"
        :value="'__ADD_NEW__'"
        :label="`添加新客户: ${searchQuery}`"
      >
        <div class="add-customer-option">
          <el-icon><Plus /></el-icon>
          <span>添加新客户: {{ searchQuery }}</span>
        </div>
      </el-option>
    </el-select>

    <!-- 快速添加客户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="快速添加客户"
      width="500px"
      @close="resetAddForm"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addRules"
        label-width="80px"
      >
        <el-form-item label="客户姓名" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入客户姓名" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="addForm.contact" placeholder="请输入手机号或微信号" />
        </el-form-item>
        <el-form-item label="默认地址">
          <el-input
            v-model="addForm.address"
            type="textarea"
            :rows="2"
            placeholder="请输入默认地址（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="addCustomer" :loading="adding">添加</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { customerAPI, addressAPI } from '../lib/api'

// Props
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  clearable: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showAddOption: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'customer-added'])

// 状态
const selectedCustomer = ref(props.modelValue)
const customerOptions = ref([])
const loading = ref(false)
const searchQuery = ref('')
const showAddDialog = ref(false)
const adding = ref(false)

// 添加客户表单
const addForm = reactive({
  name: '',
  contact: '',
  address: ''
})

const addFormRef = ref()

const addRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ]
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  selectedCustomer.value = newVal
})

// 搜索客户
const searchCustomers = async (query) => {
  if (!query) {
    customerOptions.value = []
    return
  }

  searchQuery.value = query
  loading.value = true

  try {
    // 使用新的搜索API
    const result = await customerAPI.getCustomers({
      search: query,
      limit: 10,
      page: 1
    })

    if (result.success) {
      customerOptions.value = result.data
    }
  } catch (error) {
    console.error('搜索客户失败:', error)
    ElMessage.error('搜索客户失败')
  } finally {
    loading.value = false
  }
}

// 格式化客户标签
const formatCustomerLabel = (customer) => {
  return `${customer.name} (${customer.contact || '无联系方式'})`
}

// 处理选择变化
const handleChange = (value) => {
  if (value === '__ADD_NEW__') {
    // 显示添加客户对话框
    addForm.name = searchQuery.value
    showAddDialog.value = true
    // 重置选择
    selectedCustomer.value = props.modelValue
    return
  }
  
  emit('update:modelValue', value)
  
  // 找到选中的客户对象
  const customer = customerOptions.value.find(c => c.id === value)
  emit('change', value, customer)
}

// 处理清除
const handleClear = () => {
  emit('update:modelValue', null)
  emit('change', null, null)
}

// 添加客户
const addCustomer = async () => {
  if (!addFormRef.value) return
  
  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        adding.value = true
        
        // 创建客户
        const customerResult = await customerAPI.createCustomer({
          name: addForm.name,
          contact: addForm.contact
        })
        
        if (customerResult.success) {
          const newCustomer = customerResult.data
          
          // 如果有地址，创建默认地址
          if (addForm.address.trim()) {
            await addressAPI.createAddress(newCustomer.id, {
              recipient_name: addForm.name,
              recipient_phone: addForm.contact,
              address_details: addForm.address,
              is_default: true
            })
          }
          
          // 选中新创建的客户
          selectedCustomer.value = newCustomer.id
          emit('update:modelValue', newCustomer.id)
          emit('change', newCustomer.id, newCustomer)
          emit('customer-added', newCustomer)
          
          // 将新客户添加到选项中
          customerOptions.value.unshift(newCustomer)
          
          showAddDialog.value = false
          ElMessage.success('客户添加成功')
        }
      } catch (error) {
        console.error('添加客户失败:', error)
        ElMessage.error('添加客户失败')
      } finally {
        adding.value = false
      }
    }
  })
}

// 重置添加表单
const resetAddForm = () => {
  Object.assign(addForm, {
    name: '',
    contact: '',
    address: ''
  })
  if (addFormRef.value) {
    addFormRef.value.resetFields()
  }
}

// 初始化时如果有值，加载对应的客户信息
const initializeCustomer = async () => {
  if (props.modelValue) {
    try {
      const result = await customerAPI.getCustomer(props.modelValue)
      if (result.success) {
        customerOptions.value = [result.data]
      }
    } catch (error) {
      console.error('加载客户信息失败:', error)
    }
  }
}

// 组件挂载时初始化
nextTick(() => {
  initializeCustomer()
})
</script>

<style scoped>
.customer-search-select {
  width: 100%;
}

.customer-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.customer-main {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.customer-name {
  font-weight: bold;
  color: #303133;
  font-size: 14px;
}

.customer-contact {
  color: #909399;
  font-size: 12px;
  margin-top: 2px;
}

.customer-meta {
  margin-left: 8px;
}

.add-customer-option {
  display: flex;
  align-items: center;
  color: #409eff;
  font-weight: bold;
}

.add-customer-option .el-icon {
  margin-right: 8px;
}
</style>
