<template>
  <div class="orders-view">
    <div class="page-header">
      <h2>订单管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新增订单
      </el-button>
    </div>

    <el-card>
      <div class="search-bar">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchQuery"
              placeholder="搜索订单号或客户姓名"
              prefix-icon="Search"
              clearable
            />
          </el-col>
          <el-col :span="5">
            <el-select v-model="statusFilter" placeholder="订单状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="待处理" value="pending" />
              <el-option label="生产中" value="production" />
              <el-option label="配送中" value="delivery" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="paymentFilter" placeholder="付款状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="已付款" value="paid" />
              <el-option label="未付款" value="unpaid" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="default"
            />
          </el-col>
        </el-row>
      </div>

      <el-table :data="filteredOrders" style="width: 100%" v-loading="ordersStore.loading">
        <el-table-column prop="id" label="订单号" width="100" />
        <el-table-column label="客户姓名">
          <template #default="scope">
            {{ scope.row.customers?.name || '未知客户' }}
          </template>
        </el-table-column>
        <el-table-column prop="delivery_datetime" label="送货时间">
          <template #default="scope">
            {{ formatDateTime(scope.row.delivery_datetime) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_price" label="总金额">
          <template #default="scope">
            ¥{{ parseFloat(scope.row.total_price || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="付款状态" width="120">
          <template #default="scope">
            <el-switch
              :model-value="scope.row.payment_status === 'paid'"
              @change="(value) => updatePaymentStatus(scope.row, value ? 'paid' : 'unpaid')"
              active-text="已付款"
              inactive-text="未付款"
              active-color="#67c23a"
              inactive-color="#f56c6c"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column prop="order_status" label="订单状态" width="150">
          <template #default="scope">
            <el-select
              :model-value="scope.row.order_status"
              @change="(value) => updateOrderStatus(scope.row, value)"
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="status in getAvailableStatuses(scope.row.order_status)"
                :key="status.value"
                :label="status.label"
                :value="status.value"
                :disabled="!canChangeToStatus(scope.row, status.value)"
              >
                <div class="status-option">
                  <el-tag :type="status.type" size="small">{{ status.label }}</el-tag>
                </div>
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewOrder(scope.row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="editOrder(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteOrder(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑订单对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingOrder ? '编辑订单' : '新增订单'"
      width="900px"
      @close="resetForm"
      :close-on-click-modal="false"
    >
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" class="order-steps">
        <el-step title="客户信息" description="选择客户和配送信息" />
        <el-step title="商品选择" description="选择商品和数量" />
        <el-step title="订单确认" description="确认订单信息" />
      </el-steps>

      <el-form
        ref="orderFormRef"
        :model="orderForm"
        :rules="orderRules"
        label-width="120px"
        class="order-form"
      >
        <!-- 步骤1: 客户信息 -->
        <div v-show="currentStep === 0" class="form-step">
          <div class="step-title">
            <h3>客户信息</h3>
            <p>请选择客户并填写配送信息</p>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="选择客户" prop="customer_id">
                <CustomerSearchSelect
                  v-model="orderForm.customer_id"
                  @change="handleCustomerChange"
                  @customer-added="handleCustomerAdded"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="送货时间" prop="delivery_datetime">
                <el-date-picker
                  v-model="orderForm.delivery_datetime"
                  type="datetime"
                  placeholder="选择送货时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="送货地址" prop="delivery_address">
            <div class="address-selection">
              <div class="address-selector-wrapper">
                <AddressSelector
                  v-if="orderForm.customer_id"
                  v-model="selectedAddressId"
                  :customer-id="orderForm.customer_id"
                  @change="handleAddressChange"
                  @address-added="handleAddressAdded"
                  placeholder="选择已保存的地址（可选）"
                />
                <el-button
                  v-if="selectedAddressId"
                  type="text"
                  @click="clearSelectedAddress"
                  style="margin-left: 8px;"
                >
                  清除选择
                </el-button>
              </div>
              <el-input
                v-model="orderForm.delivery_address"
                type="textarea"
                :rows="3"
                placeholder="请输入详细的送货地址"
                style="margin-top: 8px;"
              />
              <div class="address-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>您可以从上方选择已保存的地址，或直接在下方输入新地址</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="orderForm.notes"
              type="textarea"
              :rows="2"
              placeholder="请输入备注信息（可选）"
            />
          </el-form-item>
        </div>

        <!-- 步骤2: 商品选择 -->
        <div v-show="currentStep === 1" class="form-step">
          <div class="step-title">
            <h3>商品选择</h3>
            <p>请选择商品并设置数量</p>
          </div>

          <el-form-item label="选择商品" prop="items">
            <ProductSelector
              v-model="orderForm.items"
              @total-change="handleTotalChange"
            />
          </el-form-item>
        </div>

        <!-- 步骤3: 订单确认 -->
        <div v-show="currentStep === 2" class="form-step">
          <div class="step-title">
            <h3>订单确认</h3>
            <p>请确认订单信息并设置状态</p>
          </div>

          <!-- 订单摘要 -->
          <div class="order-summary">
            <el-card>
              <template #header>
                <span>订单摘要</span>
              </template>

              <el-descriptions :column="2" border>
                <el-descriptions-item label="客户">
                  {{ getSelectedCustomerName() }}
                </el-descriptions-item>
                <el-descriptions-item label="送货时间">
                  {{ formatDateTime(orderForm.delivery_datetime) }}
                </el-descriptions-item>
                <el-descriptions-item label="送货地址" :span="2">
                  {{ orderForm.delivery_address }}
                </el-descriptions-item>
                <el-descriptions-item label="商品数量">
                  {{ orderForm.items.length }}种商品
                </el-descriptions-item>
                <el-descriptions-item label="总金额">
                  <span class="total-amount">¥{{ orderForm.total_price.toFixed(2) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="备注" :span="2">
                  {{ orderForm.notes || '无' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </div>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-form-item label="付款状态">
                <el-select v-model="orderForm.payment_status" style="width: 100%">
                  <el-option label="未付款" value="unpaid" />
                  <el-option label="已付款" value="paid" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="订单状态">
                <el-select v-model="orderForm.order_status" style="width: 100%">
                  <el-option label="待处理" value="pending" />
                  <el-option label="生产中" value="production" />
                  <el-option label="配送中" value="delivery" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已取消" value="cancelled" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="showAddDialog = false">取消</el-button>
          </div>
          <div class="footer-right">
            <el-button
              v-if="currentStep > 0"
              @click="prevStep"
            >
              上一步
            </el-button>
            <el-button
              v-if="currentStep < 2"
              type="primary"
              @click="nextStep"
              :disabled="!canProceedToNextStep()"
            >
              下一步
            </el-button>
            <el-button
              v-if="currentStep === 2"
              type="primary"
              @click="saveOrder"
              :loading="saving"
            >
              {{ editingOrder ? '更新订单' : '创建订单' }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="订单详情"
      width="900px"
      @close="currentOrder = null"
    >
      <div v-if="currentOrder">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ currentOrder.id }}</el-descriptions-item>
          <el-descriptions-item label="客户姓名">{{ currentOrder.customers?.name }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ currentOrder.customers?.contact }}</el-descriptions-item>
          <el-descriptions-item label="送货时间">{{ formatDateTime(currentOrder.delivery_datetime) }}</el-descriptions-item>
          <el-descriptions-item label="送货地址" :span="2">{{ currentOrder.delivery_address }}</el-descriptions-item>
          <el-descriptions-item label="付款状态">
            <el-tag :type="getPaymentStatusType(currentOrder.payment_status)">
              {{ getPaymentStatusText(currentOrder.payment_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusType(currentOrder.order_status)">
              {{ getOrderStatusText(currentOrder.order_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总金额">¥{{ parseFloat(currentOrder.total_price || 0).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentOrder.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentOrder.notes || '无' }}</el-descriptions-item>
        </el-descriptions>

        <el-tabs v-model="activeTab" style="margin-top: 20px;">
          <el-tab-pane label="订单商品" name="items">
            <el-table :data="currentOrder.order_items || []" style="width: 100%">
              <el-table-column label="商品名称">
                <template #default="scope">
                  {{ scope.row.products?.name || '商品已删除' }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" />
              <el-table-column label="单价">
                <template #default="scope">
                  ¥{{ parseFloat(scope.row.price_at_order || 0).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="小计">
                <template #default="scope">
                  ¥{{ (parseFloat(scope.row.price_at_order || 0) * parseInt(scope.row.quantity || 0)).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="自定义属性">
                <template #default="scope">
                  <span v-if="scope.row.selected_attributes">
                    <el-tag
                      v-for="(value, key) in scope.row.selected_attributes"
                      :key="key"
                      size="small"
                      style="margin-right: 5px;"
                    >
                      {{ key }}: {{ value }}
                    </el-tag>
                  </span>
                  <span v-else>无</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="状态历史" name="history">
            <el-timeline>
              <el-timeline-item
                v-for="(history, index) in getOrderStatusHistory(currentOrder)"
                :key="index"
                :timestamp="formatDateTime(history.created_at)"
                :type="getStatusHistoryType(history.to_status)"
              >
                <div class="status-history-item">
                  <div class="status-change">
                    <span v-if="history.from_status" class="from-status">
                      {{ getOrderStatusText(history.from_status) }}
                    </span>
                    <el-icon v-if="history.from_status" class="arrow-icon"><ArrowRight /></el-icon>
                    <span class="to-status">{{ getOrderStatusText(history.to_status) }}</span>
                  </div>
                  <div v-if="history.notes" class="status-notes">{{ history.notes }}</div>
                  <div v-if="history.changed_by" class="changed-by">操作人：{{ history.changed_by }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowRight, InfoFilled } from '@element-plus/icons-vue'
import { useOrdersStore } from '../stores/orders'
import { useCustomersStore } from '../stores/customers'
import { useProductsStore } from '../stores/products'
import { orderAPI } from '../lib/api'
import CustomerSearchSelect from '../components/CustomerSearchSelect.vue'
import ProductSelector from '../components/ProductSelector.vue'
import AddressSelector from '../components/AddressSelector.vue'

const ordersStore = useOrdersStore()
const customersStore = useCustomersStore()
const productsStore = useProductsStore()

// 状态
const searchQuery = ref('')
const statusFilter = ref('')
const paymentFilter = ref('')
const dateRange = ref([])
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const editingOrder = ref(null)
const currentOrder = ref(null)
const saving = ref(false)
const currentStep = ref(0)
const activeTab = ref('items')
const selectedAddressId = ref(null)

// 表单数据
const orderForm = reactive({
  customer_id: null,
  delivery_datetime: '',
  delivery_address: '',
  payment_status: 'unpaid',
  order_status: 'pending',
  notes: '',
  items: [],
  total_price: 0
})

// 表单验证规则
const orderRules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  delivery_datetime: [
    { required: true, message: '请选择送货时间', trigger: 'change' }
  ],
  delivery_address: [
    { required: true, message: '请输入送货地址', trigger: 'blur' }
  ],
  items: [
    {
      type: 'array',
      required: true,
      min: 1,
      message: '请至少选择一个商品',
      trigger: 'change'
    }
  ]
}

const orderFormRef = ref()

// 计算属性
const filteredOrders = computed(() => {
  return ordersStore.searchOrders(searchQuery.value, {
    orderStatus: statusFilter.value,
    paymentStatus: paymentFilter.value,
    dateRange: dateRange.value
  })
})

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    'pending': 'danger',
    'production': 'info',
    'delivery': 'warning',
    'completed': 'success',
    'cancelled': ''
  }
  return statusMap[status] || 'info'
}

const getPaymentStatusType = (status) => {
  return status === 'paid' ? 'success' : 'danger'
}

// 获取状态文本
const getOrderStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getPaymentStatusText = (status) => {
  return status === 'paid' ? '已付款' : '未付款'
}

// 查看订单详情
const viewOrder = (order) => {
  currentOrder.value = order
  showDetailDialog.value = true
}

// 编辑订单
const editOrder = (order) => {
  editingOrder.value = order
  Object.assign(orderForm, {
    customer_id: order.customer_id,
    delivery_datetime: order.delivery_datetime,
    delivery_address: order.delivery_address,
    payment_status: order.payment_status,
    order_status: order.order_status,
    notes: order.notes || '',
    items: order.order_items || []
  })
  showAddDialog.value = true
}

// 删除订单
const deleteOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除订单 #${order.id} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await ordersStore.deleteOrder(order.id)
  } catch (error) {
    if (error.message) {
      console.error('删除订单失败:', error)
    }
  }
}

// 订单状态定义
const orderStatuses = [
  { value: 'pending', label: '待处理', type: 'danger' },
  { value: 'production', label: '生产中', type: 'info' },
  { value: 'delivery', label: '配送中', type: 'warning' },
  { value: 'completed', label: '已完成', type: 'success' },
  { value: 'cancelled', label: '已取消', type: '' }
]

// 状态流转规则
const statusFlowRules = {
  'pending': ['production', 'cancelled'],
  'production': ['delivery', 'cancelled'],
  'delivery': ['completed', 'cancelled'],
  'completed': [], // 已完成不能再变更
  'cancelled': [] // 已取消不能再变更
}

// 获取可用状态
const getAvailableStatuses = (currentStatus) => {
  const availableStatusValues = [currentStatus, ...(statusFlowRules[currentStatus] || [])]
  return orderStatuses.filter(status => availableStatusValues.includes(status.value))
}

// 检查是否可以变更到指定状态
const canChangeToStatus = (order, newStatus) => {
  // 如果是当前状态，总是可以选择（但不会触发更新）
  if (order.order_status === newStatus) return true

  // 检查状态流转规则
  const allowedStatuses = statusFlowRules[order.order_status] || []
  if (!allowedStatuses.includes(newStatus)) return false

  // 特殊规则：如果订单未付款，不能完成
  if (newStatus === 'completed' && order.payment_status !== 'paid') return false

  return true
}

// 更新订单状态
const updateOrderStatus = async (order, newStatus) => {
  if (order.order_status === newStatus) return

  if (!canChangeToStatus(order, newStatus)) {
    ElMessage.warning('不能变更到该状态')
    return
  }

  try {
    const result = await orderAPI.updateOrder(order.id, {
      order_status: newStatus
    })

    if (result.success) {
      // 更新本地数据
      order.order_status = newStatus

      // 显示状态变更提示
      const statusText = orderStatuses.find(s => s.value === newStatus)?.label || newStatus
      ElMessage.success(`订单状态已更新为：${statusText}`)

      // 刷新订单列表
      await ordersStore.fetchOrders()
    }
  } catch (error) {
    console.error('更新订单状态失败:', error)
    ElMessage.error('更新订单状态失败')
  }
}

// 更新付款状态
const updatePaymentStatus = async (order, newStatus) => {
  try {
    const result = await orderAPI.updateOrder(order.id, {
      payment_status: newStatus
    })

    if (result.success) {
      order.payment_status = newStatus
      ElMessage.success(`付款状态已更新`)
      await ordersStore.fetchOrders()
    }
  } catch (error) {
    console.error('更新付款状态失败:', error)
    ElMessage.error('更新付款状态失败')
  }
}

// 保存订单
const saveOrder = async () => {
  if (!orderFormRef.value) return

  await orderFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saving.value = true

        // 转换商品数据格式
        const orderItems = orderForm.items.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
          price_at_order: item.product.price,
          selected_attributes: item.selectedAttributes || {}
        }))

        const orderData = {
          customer_id: orderForm.customer_id,
          delivery_datetime: orderForm.delivery_datetime,
          delivery_address: orderForm.delivery_address,
          payment_status: orderForm.payment_status,
          order_status: orderForm.order_status,
          notes: orderForm.notes,
          total_price: orderForm.total_price,
          order_items: orderItems
        }

        let result
        if (editingOrder.value) {
          result = await ordersStore.updateOrder(editingOrder.value.id, orderData)
        } else {
          result = await ordersStore.createOrder(orderData)
        }

        if (result.success) {
          showAddDialog.value = false
          resetForm()
          ElMessage.success(editingOrder.value ? '订单更新成功' : '订单创建成功')
        }
      } catch (error) {
        console.error('保存订单失败:', error)
        ElMessage.error('保存订单失败')
      } finally {
        saving.value = false
      }
    }
  })
}

// 处理客户选择变化
const handleCustomerChange = async (customerId, customer) => {
  // 重置地址选择
  selectedAddressId.value = null
  orderForm.delivery_address = ''

  if (customer) {
    // 地址选择器会自动加载客户地址并选择默认地址
    console.log('客户已选择:', customer.name)
  }
}

// 处理地址选择变化
const handleAddressChange = (addressId, address) => {
  if (address) {
    orderForm.delivery_address = address.address_details
  } else {
    orderForm.delivery_address = ''
  }
}

// 清除地址选择
const clearSelectedAddress = () => {
  selectedAddressId.value = null
  // 保留用户手动输入的地址
}

// 处理新地址添加
const handleAddressAdded = (address) => {
  ElMessage.success(`地址添加成功`)
}

// 处理新客户添加
const handleCustomerAdded = (customer) => {
  ElMessage.success(`客户 ${customer.name} 添加成功`)
  // 刷新客户列表
  customersStore.fetchCustomers()
}

// 处理总价变化
const handleTotalChange = (totalAmount) => {
  orderForm.total_price = totalAmount
}

// 步骤控制
const nextStep = async () => {
  if (currentStep.value === 0) {
    // 验证第一步的必填字段
    if (!orderForm.customer_id) {
      ElMessage.warning('请选择客户')
      return
    }
    if (!orderForm.delivery_datetime) {
      ElMessage.warning('请选择送货时间')
      return
    }
    if (!orderForm.delivery_address) {
      ElMessage.warning('请输入送货地址')
      return
    }

    // 验证通过，进入下一步
    currentStep.value++
  } else if (currentStep.value === 1) {
    // 验证商品选择
    if (orderForm.items.length === 0) {
      ElMessage.warning('请至少选择一个商品')
      return
    }
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const canProceedToNextStep = () => {
  if (currentStep.value === 0) {
    return orderForm.customer_id && orderForm.delivery_datetime && orderForm.delivery_address
  } else if (currentStep.value === 1) {
    return orderForm.items.length > 0
  }
  return true
}

// 获取选中客户名称
const getSelectedCustomerName = () => {
  if (!orderForm.customer_id) return '未选择'

  const customer = customersStore.customers.find(c => c.id === orderForm.customer_id)
  return customer ? `${customer.name} (${customer.contact})` : '未知客户'
}

// 获取订单状态历史（模拟数据，实际应从后端获取）
const getOrderStatusHistory = (order) => {
  if (!order) return []

  // 模拟状态历史数据
  const history = [
    {
      from_status: null,
      to_status: 'pending',
      created_at: order.created_at,
      changed_by: '系统',
      notes: '订单创建'
    }
  ]

  // 如果当前状态不是pending，添加状态变更记录
  if (order.order_status !== 'pending') {
    history.push({
      from_status: 'pending',
      to_status: order.order_status,
      created_at: order.updated_at || order.created_at,
      changed_by: '操作员',
      notes: '状态更新'
    })
  }

  return history.reverse() // 最新的在前面
}

// 获取状态历史的时间线类型
const getStatusHistoryType = (status) => {
  const typeMap = {
    'pending': 'danger',
    'production': 'info',
    'delivery': 'warning',
    'completed': 'success',
    'cancelled': ''
  }
  return typeMap[status] || 'info'
}

// 重置表单
const resetForm = () => {
  editingOrder.value = null
  currentStep.value = 0
  selectedAddressId.value = null
  Object.assign(orderForm, {
    customer_id: null,
    delivery_datetime: '',
    delivery_address: '',
    payment_status: 'unpaid',
    order_status: 'pending',
    notes: '',
    items: [],
    total_price: 0
  })
}

// 组件挂载时获取数据
onMounted(async () => {
  await Promise.all([
    ordersStore.fetchOrders(),
    customersStore.fetchCustomers(),
    productsStore.fetchProducts()
  ])
})
</script>

<style scoped>
.orders-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.search-bar {
  margin-bottom: 20px;
}

.total-price-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-amount {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.price-note {
  font-size: 12px;
  color: #909399;
}

/* 步骤式表单样式 */
.order-steps {
  margin-bottom: 30px;
}

.order-form {
  min-height: 400px;
}

.form-step {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-title {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.step-title h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.step-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.order-summary {
  margin-bottom: 20px;
}

.total-amount {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 8px;
}

/* 状态管理样式 */
.status-option {
  width: 100%;
}

.status-history-item {
  padding: 8px 0;
}

.status-change {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.from-status {
  color: #909399;
  text-decoration: line-through;
}

.to-status {
  color: #409eff;
  font-weight: bold;
}

.arrow-icon {
  color: #909399;
}

.status-notes {
  font-size: 12px;
  color: #606266;
  margin-bottom: 2px;
}

.changed-by {
  font-size: 11px;
  color: #909399;
}

/* 地址选择样式 */
.address-selection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.address-selector-wrapper {
  display: flex;
  align-items: center;
}

.address-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.address-tip .el-icon {
  font-size: 14px;
}
</style>
