package services

import (
	"fmt"
	"time"

	"shop-order-backend/internal/models"
)

type LunarService struct{}

func NewLunarService() *LunarService {
	return &LunarService{}
}

// 农历月份名称
var lunarMonths = []string{
	"", "正月", "二月", "三月", "四月", "五月", "六月",
	"七月", "八月", "九月", "十月", "冬月", "腊月",
}

// 农历日期名称
var lunarDays = []string{
	"", "初一", "初二", "初三", "初四", "初五", "初六", "初七", "初八", "初九", "初十",
	"十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十",
	"廿一", "廿二", "廿三", "廿四", "廿五", "廿六", "廿七", "廿八", "廿九", "三十",
}

// 天干
var tiangan = []string{"甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"}

// 地支
var dizhi = []string{"子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"}

// 生肖
var zodiac = []string{"鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"}

// 农历数据表（1900-2100年）- 简化版本
// 实际项目中应该使用完整的农历数据表
var lunarInfo = []int{
	0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2,
	0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977,
	// ... 更多数据
}

// SolarToLunar 公历转农历
func (s *LunarService) SolarToLunar(date time.Time) (*models.LunarDate, error) {
	// 简化的农历转换算法
	// 实际项目中应该使用更精确的算法或第三方库
	
	year := date.Year()
	month := int(date.Month())
	day := date.Day()
	
	// 基础计算（这里是简化版本）
	lunarYear := year
	lunarMonth := month
	lunarDay := day
	
	// 调整农历日期（简化处理）
	if month <= 2 {
		lunarYear--
		lunarMonth += 10
	} else {
		lunarMonth -= 2
	}
	
	// 确保月份在有效范围内
	if lunarMonth > 12 {
		lunarMonth = 12
	}
	if lunarMonth < 1 {
		lunarMonth = 1
	}
	
	// 确保日期在有效范围内
	if lunarDay > 30 {
		lunarDay = 30
	}
	if lunarDay < 1 {
		lunarDay = 1
	}
	
	// 生成农历信息
	lunarDate := &models.LunarDate{
		Year:    lunarYear,
		Month:   lunarMonth,
		Day:     lunarDay,
		MonthCn: s.getMonthName(lunarMonth),
		DayCn:   s.getDayName(lunarDay),
		YearCn:  s.getYearName(lunarYear),
	}
	
	// 检查是否为传统节日
	festival := s.getFestival(lunarMonth, lunarDay)
	if festival != "" {
		lunarDate.Festival = festival
	}
	
	return lunarDate, nil
}

// getMonthName 获取农历月份名称
func (s *LunarService) getMonthName(month int) string {
	if month >= 1 && month <= 12 {
		return lunarMonths[month]
	}
	return "未知"
}

// getDayName 获取农历日期名称
func (s *LunarService) getDayName(day int) string {
	if day >= 1 && day <= 30 {
		return lunarDays[day]
	}
	return "未知"
}

// getYearName 获取农历年份名称（天干地支）
func (s *LunarService) getYearName(year int) string {
	// 计算天干地支
	tianganIndex := (year - 4) % 10
	dizhiIndex := (year - 4) % 12
	zodiacIndex := (year - 4) % 12
	
	return fmt.Sprintf("%s%s年 (%s年)", 
		tiangan[tianganIndex], 
		dizhi[dizhiIndex], 
		zodiac[zodiacIndex])
}

// getFestival 获取传统节日
func (s *LunarService) getFestival(month, day int) string {
	festivals := map[string]string{
		"1-1":   "春节",
		"1-15":  "元宵节",
		"2-2":   "龙抬头",
		"5-5":   "端午节",
		"7-7":   "七夕节",
		"7-15":  "中元节",
		"8-15":  "中秋节",
		"9-9":   "重阳节",
		"12-8":  "腊八节",
		"12-23": "小年",
		"12-30": "除夕",
	}
	
	key := fmt.Sprintf("%d-%d", month, day)
	return festivals[key]
}

// GetCalendarInfo 获取完整的日历信息
func (s *LunarService) GetCalendarInfo(date time.Time, holiday *models.Holiday) (*models.CalendarDate, error) {
	lunar, err := s.SolarToLunar(date)
	if err != nil {
		return nil, err
	}
	
	// 判断是否为今天
	today := time.Now()
	isToday := date.Format("2006-01-02") == today.Format("2006-01-02")
	
	// 判断是否为周末
	weekday := date.Weekday()
	isWeekend := weekday == time.Saturday || weekday == time.Sunday
	
	calendarDate := &models.CalendarDate{
		Date:      date.Format("2006-01-02"),
		Solar:     date.Format("01-02"),
		Lunar:     lunar,
		IsToday:   isToday,
		IsWeekend: isWeekend,
	}
	
	if holiday != nil {
		calendarDate.Holiday = holiday
	}
	
	return calendarDate, nil
}

// GetMonthCalendar 获取整月的日历信息
func (s *LunarService) GetMonthCalendar(year, month int, holidays []*models.Holiday) ([]*models.CalendarDate, error) {
	// 创建月份的第一天
	firstDay := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	
	// 获取该月的天数
	nextMonth := firstDay.AddDate(0, 1, 0)
	lastDay := nextMonth.AddDate(0, 0, -1)
	daysInMonth := lastDay.Day()
	
	// 创建节假日映射
	holidayMap := make(map[string]*models.Holiday)
	for _, holiday := range holidays {
		holidayMap[holiday.Date.Format("2006-01-02")] = holiday
	}
	
	var calendar []*models.CalendarDate
	
	// 生成每一天的日历信息
	for day := 1; day <= daysInMonth; day++ {
		date := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.Local)
		dateStr := date.Format("2006-01-02")
		
		holiday := holidayMap[dateStr]
		calendarDate, err := s.GetCalendarInfo(date, holiday)
		if err != nil {
			return nil, err
		}
		
		calendar = append(calendar, calendarDate)
	}
	
	return calendar, nil
}
