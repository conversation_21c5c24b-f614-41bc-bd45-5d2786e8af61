<template>
  <div class="address-selector">
    <el-select
      v-model="selectedAddress"
      :placeholder="placeholder"
      :clearable="clearable"
      :disabled="disabled"
      style="width: 100%"
      @change="handleChange"
      @clear="handleClear"
    >
      <el-option
        v-for="address in addresses"
        :key="address.id"
        :label="formatAddressLabel(address)"
        :value="address.id"
      >
        <div class="address-option">
          <div class="address-header">
            <span class="recipient-name">{{ address.recipient_name }}</span>
            <span class="recipient-phone">{{ address.recipient_phone }}</span>
            <el-tag v-if="address.is_default" type="success" size="small">默认</el-tag>
          </div>
          <div class="address-details">
            {{ address.address_details }}
          </div>
        </div>
      </el-option>
      
      <!-- 无地址时显示 -->
      <el-option
        v-if="addresses.length === 0 && !loading"
        disabled
        value=""
        label="暂无地址"
      />
      
      <!-- 快速添加地址选项 -->
      <el-option
        v-if="showAddOption"
        :value="'__ADD_NEW__'"
        label="添加新地址"
      >
        <div class="add-address-option">
          <el-icon><Plus /></el-icon>
          <span>添加新地址</span>
        </div>
      </el-option>
    </el-select>

    <!-- 快速添加地址对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加收货地址"
      width="600px"
      @close="resetAddForm"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addRules"
        label-width="100px"
      >
        <el-form-item label="收货人" prop="recipient_name">
          <el-input
            v-model="addForm.recipient_name"
            placeholder="请输入收货人姓名"
            clearable
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="recipient_phone">
          <el-input
            v-model="addForm.recipient_phone"
            placeholder="请输入联系电话"
            clearable
          />
        </el-form-item>

        <el-form-item label="详细地址" prop="address_details">
          <el-input
            v-model="addForm.address_details"
            type="textarea"
            :rows="3"
            placeholder="请输入详细地址"
            show-word-limit
            maxlength="200"
          />
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="addForm.is_default" />
          <span class="form-tip">设为默认后，新订单将优先使用此地址</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="addAddress" :loading="adding">添加</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { addressAPI } from '../lib/api'

// Props
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  customerId: {
    type: [Number, String],
    required: true
  },
  placeholder: {
    type: String,
    default: '请选择收货地址'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showAddOption: {
    type: Boolean,
    default: true
  },
  autoSelectDefault: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'address-added'])

// 状态
const selectedAddress = ref(props.modelValue)
const addresses = ref([])
const loading = ref(false)
const showAddDialog = ref(false)
const adding = ref(false)

// 添加地址表单
const addForm = reactive({
  recipient_name: '',
  recipient_phone: '',
  address_details: '',
  is_default: false
})

const addFormRef = ref()

const addRules = {
  recipient_name: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' }
  ],
  recipient_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  address_details: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ]
}

// 计算属性
const defaultAddress = computed(() => {
  return addresses.value.find(addr => addr.is_default)
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  selectedAddress.value = newVal
})

// 监听客户ID变化
watch(() => props.customerId, (newCustomerId) => {
  if (newCustomerId) {
    loadAddresses()
  } else {
    addresses.value = []
    selectedAddress.value = null
  }
}, { immediate: true })

// 方法
const loadAddresses = async () => {
  if (!props.customerId) return

  try {
    loading.value = true
    const result = await addressAPI.getAddresses(props.customerId)

    if (result.success) {
      addresses.value = result.data.sort((a, b) => {
        // 默认地址优先
        if (a.is_default && !b.is_default) return -1
        if (!a.is_default && b.is_default) return 1

        // 按创建时间倒序
        return new Date(b.created_at) - new Date(a.created_at)
      })

      // 自动选择默认地址
      if (props.autoSelectDefault && !selectedAddress.value && defaultAddress.value) {
        selectedAddress.value = defaultAddress.value.id
        emit('update:modelValue', defaultAddress.value.id)
        emit('change', defaultAddress.value.id, defaultAddress.value)
      }
    }
  } catch (error) {
    console.error('加载地址失败:', error)
    ElMessage.error('加载地址失败')
  } finally {
    loading.value = false
  }
}

const formatAddressLabel = (address) => {
  return `${address.recipient_name} ${address.recipient_phone}`
}

const handleChange = (value) => {
  if (value === '__ADD_NEW__') {
    // 显示添加地址对话框
    showAddDialog.value = true
    // 重置选择
    selectedAddress.value = props.modelValue
    return
  }
  
  emit('update:modelValue', value)
  
  // 找到选中的地址对象
  const address = addresses.value.find(addr => addr.id === value)
  emit('change', value, address)
}

const handleClear = () => {
  emit('update:modelValue', null)
  emit('change', null, null)
}

const addAddress = async () => {
  if (!addFormRef.value) return
  
  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        adding.value = true
        
        const result = await addressAPI.createAddress(props.customerId, addForm)
        
        if (result.success) {
          const newAddress = result.data
          
          // 添加到地址列表
          addresses.value.unshift(newAddress)
          
          // 选中新添加的地址
          selectedAddress.value = newAddress.id
          emit('update:modelValue', newAddress.id)
          emit('change', newAddress.id, newAddress)
          emit('address-added', newAddress)
          
          showAddDialog.value = false
          ElMessage.success('地址添加成功')
        }
      } catch (error) {
        console.error('添加地址失败:', error)
        ElMessage.error('添加地址失败')
      } finally {
        adding.value = false
      }
    }
  })
}

const resetAddForm = () => {
  Object.assign(addForm, {
    recipient_name: '',
    recipient_phone: '',
    address_details: '',
    is_default: false
  })
  if (addFormRef.value) {
    addFormRef.value.resetFields()
  }
}

// 公开方法
const refreshAddresses = () => {
  loadAddresses()
}

const getSelectedAddress = () => {
  return addresses.value.find(addr => addr.id === selectedAddress.value)
}

// 暴露方法给父组件
defineExpose({
  refreshAddresses,
  getSelectedAddress
})
</script>

<style scoped>
.address-selector {
  width: 100%;
}

.address-option {
  padding: 8px 0;
}

.address-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.recipient-name {
  font-weight: bold;
  color: #303133;
}

.recipient-phone {
  color: #909399;
  font-size: 12px;
}

.address-details {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.add-address-option {
  display: flex;
  align-items: center;
  color: #409eff;
  font-weight: bold;
}

.add-address-option .el-icon {
  margin-right: 8px;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}
</style>
