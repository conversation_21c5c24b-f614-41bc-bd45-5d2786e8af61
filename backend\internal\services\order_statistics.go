package services

import (
	"time"

	"xorm.io/xorm"
)

type OrderStatisticsService struct {
	db *xorm.Engine
}

func NewOrderStatisticsService(db *xorm.Engine) *OrderStatisticsService {
	return &OrderStatisticsService{db: db}
}

// OrderStatistics 订单统计结果
type OrderStatistics struct {
	Date         string  `json:"date"`
	Category     string  `json:"category"`
	OrderCount   int     `json:"order_count"`
	ItemCount    int     `json:"item_count"`
	TotalAmount  float64 `json:"total_amount"`
	ProductCount int     `json:"product_count"`
}

// DailyOrderSummary 每日订单汇总
type DailyOrderSummary struct {
	Date          string                `json:"date"`
	TotalOrders   int                   `json:"total_orders"`
	TotalAmount   float64               `json:"total_amount"`
	TotalItems    int                   `json:"total_items"`
	CategoryStats []*CategoryStatistics `json:"category_stats"`
	ProductStats  []*ProductStatistics  `json:"product_stats"`
	PaymentStats  map[string]int        `json:"payment_stats"`
	StatusStats   map[string]int        `json:"status_stats"`
}

// CategoryStatistics 分类统计
type CategoryStatistics struct {
	Category    string  `json:"category"`
	ItemCount   int     `json:"item_count"`
	TotalAmount float64 `json:"total_amount"`
	OrderCount  int     `json:"order_count"`
}

// ProductStatistics 商品统计
type ProductStatistics struct {
	ProductID   int64   `json:"product_id"`
	ProductName string  `json:"product_name"`
	Category    string  `json:"category"`
	Quantity    int     `json:"quantity"`
	TotalAmount float64 `json:"total_amount"`
	OrderCount  int     `json:"order_count"`
}

// GetOrderStatisticsByDateRange 获取日期范围内的订单统计
func (s *OrderStatisticsService) GetOrderStatisticsByDateRange(startDate, endDate time.Time, groupBy string) ([]*OrderStatistics, error) {
	var results []*OrderStatistics

	query := `
		SELECT 
			DATE(o.delivery_datetime) as date,
			p.category,
			COUNT(DISTINCT o.id) as order_count,
			COUNT(oi.id) as item_count,
			SUM(oi.quantity * oi.price_at_order) as total_amount,
			COUNT(DISTINCT oi.product_id) as product_count
		FROM orders o
		JOIN order_items oi ON o.id = oi.order_id
		JOIN products p ON oi.product_id = p.id
		WHERE DATE(o.delivery_datetime) BETWEEN ? AND ?
	`

	switch groupBy {
	case "category":
		query += " GROUP BY DATE(o.delivery_datetime), p.category"
	case "date":
		query += " GROUP BY DATE(o.delivery_datetime)"
	default:
		query += " GROUP BY DATE(o.delivery_datetime), p.category"
	}

	query += " ORDER BY date, p.category"

	err := s.db.SQL(query, startDate.Format("2006-01-02"), endDate.Format("2006-01-02")).Find(&results)
	return results, err
}

// GetDailyOrderSummary 获取指定日期的订单汇总
func (s *OrderStatisticsService) GetDailyOrderSummary(date time.Time) (*DailyOrderSummary, error) {
	dateStr := date.Format("2006-01-02")

	summary := &DailyOrderSummary{
		Date:          dateStr,
		CategoryStats: []*CategoryStatistics{},
		ProductStats:  []*ProductStatistics{},
		PaymentStats:  make(map[string]int),
		StatusStats:   make(map[string]int),
	}

	// 获取基础统计
	basicQuery := `
		SELECT 
			COUNT(DISTINCT o.id) as total_orders,
			SUM(o.total_price) as total_amount,
			COUNT(oi.id) as total_items
		FROM orders o
		LEFT JOIN order_items oi ON o.id = oi.order_id
		WHERE DATE(o.delivery_datetime) = ?
	`

	type BasicStats struct {
		TotalOrders int     `json:"total_orders"`
		TotalAmount float64 `json:"total_amount"`
		TotalItems  int     `json:"total_items"`
	}

	var basicStats BasicStats
	_, err := s.db.SQL(basicQuery, dateStr).Get(&basicStats)
	if err != nil {
		return nil, err
	}

	summary.TotalOrders = basicStats.TotalOrders
	summary.TotalAmount = basicStats.TotalAmount
	summary.TotalItems = basicStats.TotalItems

	// 获取分类统计
	categoryQuery := `
		SELECT 
			p.category,
			COUNT(DISTINCT o.id) as order_count,
			COUNT(oi.id) as item_count,
			SUM(oi.quantity * oi.price_at_order) as total_amount
		FROM orders o
		JOIN order_items oi ON o.id = oi.order_id
		JOIN products p ON oi.product_id = p.id
		WHERE DATE(o.delivery_datetime) = ?
		GROUP BY p.category
		ORDER BY total_amount DESC
	`

	err = s.db.SQL(categoryQuery, dateStr).Find(&summary.CategoryStats)
	if err != nil {
		return nil, err
	}

	// 获取商品统计
	productQuery := `
		SELECT 
			p.id as product_id,
			p.name as product_name,
			p.category,
			SUM(oi.quantity) as quantity,
			SUM(oi.quantity * oi.price_at_order) as total_amount,
			COUNT(DISTINCT o.id) as order_count
		FROM orders o
		JOIN order_items oi ON o.id = oi.order_id
		JOIN products p ON oi.product_id = p.id
		WHERE DATE(o.delivery_datetime) = ?
		GROUP BY p.id, p.name, p.category
		ORDER BY quantity DESC
		LIMIT 10
	`

	err = s.db.SQL(productQuery, dateStr).Find(&summary.ProductStats)
	if err != nil {
		return nil, err
	}

	// 获取付款状态统计
	var paymentStats []struct {
		PaymentStatus string `json:"payment_status"`
		Count         int    `json:"count"`
	}

	paymentQuery := `
		SELECT payment_status, COUNT(*) as count
		FROM orders
		WHERE DATE(delivery_datetime) = ?
		GROUP BY payment_status
	`

	err = s.db.SQL(paymentQuery, dateStr).Find(&paymentStats)
	if err != nil {
		return nil, err
	}

	for _, stat := range paymentStats {
		summary.PaymentStats[stat.PaymentStatus] = stat.Count
	}

	// 获取订单状态统计
	var statusStats []struct {
		OrderStatus string `json:"order_status"`
		Count       int    `json:"count"`
	}

	statusQuery := `
		SELECT order_status, COUNT(*) as count
		FROM orders
		WHERE DATE(delivery_datetime) = ?
		GROUP BY order_status
	`

	err = s.db.SQL(statusQuery, dateStr).Find(&statusStats)
	if err != nil {
		return nil, err
	}

	for _, stat := range statusStats {
		summary.StatusStats[stat.OrderStatus] = stat.Count
	}

	return summary, nil
}

// GetMonthlyOrderSummary 获取月度订单汇总
func (s *OrderStatisticsService) GetMonthlyOrderSummary(year, month int) ([]*DailyOrderSummary, error) {
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, -1)

	var summaries []*DailyOrderSummary

	// 遍历每一天
	for d := startDate; !d.After(endDate); d = d.AddDate(0, 0, 1) {
		summary, err := s.GetDailyOrderSummary(d)
		if err != nil {
			continue
		}
		summaries = append(summaries, summary)
	}

	return summaries, nil
}

// GetCategoryTrend 获取分类趋势
func (s *OrderStatisticsService) GetCategoryTrend(category string, days int) ([]*OrderStatistics, error) {
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -days)

	var results []*OrderStatistics

	query := `
		SELECT 
			DATE(o.delivery_datetime) as date,
			p.category,
			COUNT(DISTINCT o.id) as order_count,
			COUNT(oi.id) as item_count,
			SUM(oi.quantity * oi.price_at_order) as total_amount,
			COUNT(DISTINCT oi.product_id) as product_count
		FROM orders o
		JOIN order_items oi ON o.id = oi.order_id
		JOIN products p ON oi.product_id = p.id
		WHERE DATE(o.delivery_datetime) BETWEEN ? AND ?
		AND p.category = ?
		GROUP BY DATE(o.delivery_datetime), p.category
		ORDER BY date
	`

	err := s.db.SQL(query, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), category).Find(&results)
	return results, err
}
