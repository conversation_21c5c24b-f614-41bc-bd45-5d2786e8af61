<template>
  <div class="calendar-view">
    <div class="page-header">
      <h2>生产/配送日历</h2>
      <div class="header-controls">
        <div class="date-navigation">
          <el-button @click="navigateDate('prev')" :icon="ArrowLeft" circle />
          <span class="current-date-display">{{ formatCurrentDate() }}</span>
          <el-button @click="navigateDate('next')" :icon="ArrowRight" circle />
          <el-button @click="goToToday" type="primary" size="small">今天</el-button>
        </div>
        <div class="view-controls">
          <el-radio-group v-model="viewMode">
            <el-radio-button label="month">月视图</el-radio-button>
            <el-radio-button label="week">周视图</el-radio-button>
            <el-radio-button label="year">年视图</el-radio-button>
          </el-radio-group>
          <el-button @click="refreshData" :icon="Refresh" :loading="loading" circle />
        </div>
      </div>
    </div>

    <el-card>
      <!-- 快捷键提示 -->
      <div class="keyboard-shortcuts" v-if="showShortcuts">
        <el-alert
          title="快捷键提示"
          type="info"
          :closable="true"
          @close="showShortcuts = false"
        >
          <template #default>
            <div class="shortcuts-grid">
              <div class="shortcut-item">
                <kbd>←</kbd> <kbd>→</kbd> 切换月份/年份
              </div>
              <div class="shortcut-item">
                <kbd>T</kbd> 回到今天
              </div>
              <div class="shortcut-item">
                <kbd>R</kbd> 刷新数据
              </div>
              <div class="shortcut-item">
                <kbd>1</kbd> <kbd>2</kbd> <kbd>3</kbd> 切换视图
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <div class="calendar-container" :class="{ loading }">
        <el-calendar v-model="currentDate" v-loading="loading">
          <template #date-cell="{ data }">
            <div
              class="calendar-cell"
              @click="handleDateClick(data)"
              :class="{
                'has-orders': getOrdersForDate(data.day).length > 0,
                'is-holiday': isHoliday(data.day),
                'is-weekend': isWeekend(data.day)
              }"
            >
              <div class="date-info">
                <span class="solar-date">{{ data.day.split('-')[2] }}</span>
                <span class="lunar-date">{{ getLunarDate(data.day) }}</span>
              </div>
              <div v-if="getOrdersForDate(data.day).length > 0" class="order-info">
                <el-tag size="small" type="primary">
                  {{ getOrdersForDate(data.day).length }}单
                </el-tag>
                <div class="product-summary">
                  <el-tag
                    v-for="summary in getProductSummaryForDate(data.day)"
                    :key="summary.category"
                    size="small"
                    type="info"
                    class="category-tag"
                  >
                    {{ summary.category }}: {{ summary.quantity }}
                  </el-tag>
                </div>
              </div>
              <div v-if="isHoliday(data.day)" class="holiday-mark">
                <el-tag size="small" type="danger">休</el-tag>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
    </el-card>

    <!-- 日期详情对话框 -->
    <el-dialog
      v-model="showDateDetail"
      :title="getDialogTitle()"
      width="1000px"
      @close="selectedDate = ''"
    >
      <!-- 日期汇总信息 -->
      <div class="date-summary" v-if="selectedDateOrders.length > 0">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="订单总数" :value="selectedDateOrders.length" suffix="单" />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="总金额"
              :value="getTotalAmount(selectedDateOrders)"
              prefix="¥"
              :precision="2"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="商品总数"
              :value="getTotalItems(selectedDateOrders)"
              suffix="件"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="已付款订单"
              :value="getPaidOrdersCount(selectedDateOrders)"
              suffix="单"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 商品分类汇总 -->
      <div class="category-summary" v-if="selectedDateOrders.length > 0">
        <h4>商品分类汇总</h4>
        <el-row :gutter="10">
          <el-col
            v-for="summary in getDetailedProductSummary(selectedDate)"
            :key="summary.category"
            :span="6"
          >
            <el-card class="category-card">
              <div class="category-info">
                <div class="category-name">{{ summary.category }}</div>
                <div class="category-stats">
                  <span>{{ summary.quantity }}件</span>
                  <span>¥{{ summary.amount.toFixed(2) }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 订单详情表格 -->
      <div class="orders-table">
        <h4>订单详情</h4>
        <el-table :data="selectedDateOrders" style="width: 100%" max-height="400">
          <el-table-column prop="id" label="订单号" width="80" />
          <el-table-column label="客户" width="120">
            <template #default="scope">
              {{ scope.row.customers?.name || '未知客户' }}
            </template>
          </el-table-column>
          <el-table-column label="商品" min-width="200">
            <template #default="scope">
              <div class="product-list">
                <div
                  v-for="item in scope.row.order_items"
                  :key="item.id"
                  class="product-item"
                >
                  <span class="product-name">{{ item.products?.name || '未知商品' }}</span>
                  <span class="product-quantity">x{{ item.quantity }}</span>
                  <span class="product-price">¥{{ (item.price_at_order * item.quantity).toFixed(2) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="total_price" label="总金额" width="100">
            <template #default="scope">
              ¥{{ parseFloat(scope.row.total_price || 0).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="付款状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.payment_status === 'paid' ? 'success' : 'danger'" size="small">
                {{ scope.row.payment_status === 'paid' ? '已付款' : '未付款' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="订单状态" width="100">
            <template #default="scope">
              <el-tag :type="getOrderStatusType(scope.row.order_status)" size="small">
                {{ getOrderStatusText(scope.row.order_status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="送货时间" width="120">
            <template #default="scope">
              {{ new Date(scope.row.delivery_datetime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ArrowLeft, ArrowRight, Refresh } from '@element-plus/icons-vue'
import { useOrdersStore } from '../stores/orders'
import { solarToLunar, formatLunarDate } from '../lib/lunar-utils'
import { holidayAPI } from '../lib/api'

const ordersStore = useOrdersStore()

const viewMode = ref('month')
const currentDate = ref(new Date())
const showDateDetail = ref(false)
const selectedDate = ref('')
const holidays = ref([])
const loading = ref(false)
const lunarCache = ref(new Map())
const holidayCache = ref(new Map())
const orderCache = ref(new Map())
const showShortcuts = ref(true)

// 计算属性
const selectedDateOrders = computed(() => {
  if (!selectedDate.value) return []
  return getOrdersForDate(selectedDate.value)
})

// 按日期分组的订单
const ordersByDate = computed(() => {
  const grouped = {}
  ordersStore.orders.forEach(order => {
    if (order.delivery_datetime) {
      const date = order.delivery_datetime.split('T')[0] // 获取日期部分
      if (!grouped[date]) grouped[date] = []
      grouped[date].push(order)
    }
  })
  return grouped
})

// 获取指定日期的订单（带缓存优化）
const getOrdersForDate = (date) => {
  // 检查缓存
  if (orderCache.value.has(date)) {
    return orderCache.value.get(date)
  }

  const orders = ordersByDate.value[date] || []

  // 缓存结果
  orderCache.value.set(date, orders)

  return orders
}

// 获取指定日期的商品汇总
const getProductSummaryForDate = (date) => {
  const orders = getOrdersForDate(date)
  const categoryMap = new Map()

  orders.forEach(order => {
    if (order.order_items && order.order_items.length > 0) {
      order.order_items.forEach(item => {
        const category = item.products?.category || '其他'
        const quantity = parseInt(item.quantity) || 0

        if (categoryMap.has(category)) {
          categoryMap.set(category, categoryMap.get(category) + quantity)
        } else {
          categoryMap.set(category, quantity)
        }
      })
    }
  })

  // 转换为数组并按数量排序，只显示前3个分类
  const summaries = Array.from(categoryMap.entries())
    .map(([category, quantity]) => ({ category, quantity }))
    .sort((a, b) => b.quantity - a.quantity)
    .slice(0, 3)

  return summaries
}

// 获取农历日期（带缓存）
const getLunarDate = (date) => {
  try {
    // 检查缓存
    if (lunarCache.value.has(date)) {
      return lunarCache.value.get(date)
    }

    const lunarInfo = solarToLunar(date)
    const formatted = formatLunarDate(lunarInfo)

    // 缓存结果
    lunarCache.value.set(date, formatted)

    return formatted
  } catch (error) {
    console.error('获取农历日期失败:', error)
    return ''
  }
}

// 检查是否为节假日
const isHoliday = (date) => {
  return holidays.value.some(holiday => holiday.date === date && holiday.isHoliday)
}

// 检查是否为周末
const isWeekend = (date) => {
  const day = new Date(date).getDay()
  return day === 0 || day === 6 // 0是周日，6是周六
}

// 获取状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    'pending': 'danger',
    'production': 'info',
    'delivery': 'warning',
    'completed': 'success',
    'cancelled': ''
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 点击日期单元格
const handleDateClick = (date) => {
  const orders = getOrdersForDate(date.day)
  if (orders.length > 0) {
    selectedDate.value = date.day
    showDateDetail.value = true
  }
}

// 获取节假日数据（带缓存）
const fetchHolidays = async (year) => {
  try {
    // 检查缓存
    const cacheKey = `holidays_${year}`
    if (holidayCache.value.has(cacheKey)) {
      holidays.value = holidayCache.value.get(cacheKey)
      return
    }

    loading.value = true

    // 使用后端API获取节假日数据
    const result = await holidayAPI.getHolidays({ year })

    let holidayData = []
    if (result.success && Array.isArray(result.data)) {
      holidayData = result.data.map(holiday => ({
        date: holiday.date.split('T')[0], // 只取日期部分
        name: holiday.name,
        isHoliday: holiday.type === 'holiday'
      }))
    } else {
      // 如果后端没有数据，尝试同步
      const syncResult = await holidayAPI.syncHolidays(year)
      if (syncResult.success) {
        // 重新获取数据
        const retryResult = await holidayAPI.getHolidays({ year })
        if (retryResult.success && Array.isArray(retryResult.data)) {
          holidayData = retryResult.data.map(holiday => ({
            date: holiday.date.split('T')[0],
            name: holiday.name,
            isHoliday: holiday.type === 'holiday'
          }))
        }
      }
    }

    holidays.value = holidayData
    // 缓存结果（缓存1小时）
    holidayCache.value.set(cacheKey, holidayData)
    setTimeout(() => {
      holidayCache.value.delete(cacheKey)
    }, 60 * 60 * 1000)
  } catch (error) {
    console.error('获取节假日数据失败:', error)
    // 使用默认节假日数据
    holidays.value = [
      { date: '2024-01-01', name: '元旦', isHoliday: true },
      { date: '2024-02-10', name: '春节', isHoliday: true },
      { date: '2024-02-11', name: '春节', isHoliday: true },
      { date: '2024-02-12', name: '春节', isHoliday: true },
      { date: '2024-04-04', name: '清明节', isHoliday: true },
      { date: '2024-05-01', name: '劳动节', isHoliday: true },
      { date: '2024-06-10', name: '端午节', isHoliday: true },
      { date: '2024-09-17', name: '中秋节', isHoliday: true },
      { date: '2024-10-01', name: '国庆节', isHoliday: true },
      { date: '2024-10-02', name: '国庆节', isHoliday: true },
      { date: '2024-10-03', name: '国庆节', isHoliday: true }
    ]
  } finally {
    loading.value = false
  }
}

// 格式化商品列表
const formatProducts = (orderItems) => {
  if (!orderItems || orderItems.length === 0) return '无商品'

  return orderItems.map(item => {
    const productName = item.products?.name || '未知商品'
    return `${productName} x${item.quantity}`
  }).join(', ')
}

// 获取弹窗标题
const getDialogTitle = () => {
  if (!selectedDate.value) return '订单详情'

  const date = new Date(selectedDate.value)
  const lunarInfo = solarToLunar(selectedDate.value)
  const holiday = holidays.value.find(h => h.date === selectedDate.value)

  let title = `${selectedDate.value} (${date.toLocaleDateString('zh-CN', { weekday: 'long' })})`

  if (lunarInfo.lunarDateStr) {
    title += ` 农历${lunarInfo.lunarDateStr}`
  }

  if (holiday) {
    title += ` ${holiday.name}`
  }

  return title
}

// 计算总金额
const getTotalAmount = (orders) => {
  return orders.reduce((total, order) => total + parseFloat(order.total_price || 0), 0)
}

// 计算总商品数
const getTotalItems = (orders) => {
  return orders.reduce((total, order) => {
    if (order.order_items) {
      return total + order.order_items.reduce((itemTotal, item) => itemTotal + parseInt(item.quantity || 0), 0)
    }
    return total
  }, 0)
}

// 计算已付款订单数
const getPaidOrdersCount = (orders) => {
  return orders.filter(order => order.payment_status === 'paid').length
}

// 获取详细的商品汇总
const getDetailedProductSummary = (date) => {
  const orders = getOrdersForDate(date)
  const categoryMap = new Map()

  orders.forEach(order => {
    if (order.order_items && order.order_items.length > 0) {
      order.order_items.forEach(item => {
        const category = item.products?.category || '其他'
        const quantity = parseInt(item.quantity) || 0
        const amount = parseFloat(item.price_at_order || 0) * quantity

        if (categoryMap.has(category)) {
          const existing = categoryMap.get(category)
          categoryMap.set(category, {
            quantity: existing.quantity + quantity,
            amount: existing.amount + amount
          })
        } else {
          categoryMap.set(category, { quantity, amount })
        }
      })
    }
  })

  return Array.from(categoryMap.entries())
    .map(([category, data]) => ({ category, ...data }))
    .sort((a, b) => b.amount - a.amount)
}

// 清理缓存
const clearCache = () => {
  orderCache.value.clear()
  lunarCache.value.clear()
}

// 监听订单数据变化，清理缓存
watch(() => ordersStore.orders, () => {
  clearCache()
}, { deep: true })

// 监听当前日期变化，获取对应年份的节假日
watch(currentDate, (newDate) => {
  const year = newDate.getFullYear()
  fetchHolidays(year)
  // 切换年份时清理农历缓存
  lunarCache.value.clear()
}, { immediate: true })

// 格式化当前日期显示
const formatCurrentDate = () => {
  const date = currentDate.value
  const year = date.getFullYear()
  const month = date.getMonth() + 1

  if (viewMode.value === 'year') {
    return `${year}年`
  } else {
    return `${year}年${month}月`
  }
}

// 导航日期
const navigateDate = (direction) => {
  const date = new Date(currentDate.value)

  if (viewMode.value === 'year') {
    date.setFullYear(date.getFullYear() + (direction === 'next' ? 1 : -1))
  } else if (viewMode.value === 'month') {
    date.setMonth(date.getMonth() + (direction === 'next' ? 1 : -1))
  } else if (viewMode.value === 'week') {
    date.setDate(date.getDate() + (direction === 'next' ? 7 : -7))
  }

  currentDate.value = date
}

// 回到今天
const goToToday = () => {
  currentDate.value = new Date()
}

// 刷新数据
const refreshData = async () => {
  clearCache()
  await ordersStore.fetchOrders()
  const year = currentDate.value.getFullYear()
  await fetchHolidays(year)
}

// 键盘快捷键处理
const handleKeydown = (event) => {
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return
  }

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      navigateDate('prev')
      break
    case 'ArrowRight':
      event.preventDefault()
      navigateDate('next')
      break
    case 't':
    case 'T':
      event.preventDefault()
      goToToday()
      break
    case 'r':
    case 'R':
      event.preventDefault()
      refreshData()
      break
    case '1':
      event.preventDefault()
      viewMode.value = 'month'
      break
    case '2':
      event.preventDefault()
      viewMode.value = 'week'
      break
    case '3':
      event.preventDefault()
      viewMode.value = 'year'
      break
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  await ordersStore.fetchOrders()
  const currentYear = new Date().getFullYear()
  await fetchHolidays(currentYear)

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.calendar-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.date-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-date-display {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  min-width: 120px;
  text-align: center;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.calendar-container {
  min-height: 600px;
  transition: all 0.3s ease;
}

.calendar-container.loading {
  opacity: 0.7;
  pointer-events: none;
}

.calendar-cell {
  height: 100%;
  min-height: 80px;
  padding: 4px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  overflow: hidden;
}

.calendar-cell:hover {
  background-color: #f5f7fa;
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-cell.has-orders {
  background-color: #ecf5ff;
  border: 2px solid #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.calendar-cell.is-holiday {
  background-color: #fef0f0;
  border: 1px solid #f56c6c;
}

.calendar-cell.is-holiday .solar-date {
  color: #f56c6c;
}

.calendar-cell.is-weekend {
  background-color: #fafafa;
}

.calendar-cell.is-weekend .solar-date {
  color: #909399;
}

.calendar-cell.is-holiday.has-orders {
  background: linear-gradient(135deg, #fef0f0 0%, #ecf5ff 100%);
  border: 2px solid #f56c6c;
}

.date-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 4px;
}

.solar-date {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  line-height: 1.2;
}

.lunar-date {
  font-size: 11px;
  color: #909399;
  line-height: 1.2;
  margin-top: 1px;
}

.order-info {
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  max-width: 60%;
}

.holiday-mark {
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.product-summary {
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.category-tag {
  font-size: 10px;
  padding: 1px 4px;
  margin: 0;
}

/* 弹窗样式 */
.date-summary {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.category-summary {
  margin-bottom: 20px;
}

.category-summary h4 {
  margin-bottom: 12px;
  color: #303133;
}

.category-card {
  margin-bottom: 8px;
}

.category-info {
  text-align: center;
}

.category-name {
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
}

.orders-table h4 {
  margin-bottom: 12px;
  color: #303133;
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.product-name {
  flex: 1;
  color: #303133;
}

.product-quantity {
  color: #909399;
  margin: 0 8px;
}

.product-price {
  color: #409eff;
  font-weight: bold;
}

/* 快捷键提示样式 */
.keyboard-shortcuts {
  margin-bottom: 16px;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

kbd {
  display: inline-block;
  padding: 2px 6px;
  font-size: 12px;
  line-height: 1.4;
  color: #444;
  background-color: #fafbfc;
  border: 1px solid #d1d5da;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #d1d5da;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .header-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .date-navigation {
    justify-content: center;
  }

  .view-controls {
    justify-content: center;
  }

  .shortcuts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
