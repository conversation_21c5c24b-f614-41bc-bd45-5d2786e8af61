package config

import (
	"os"
	"strconv"
	"strings"
)

type Config struct {
	// 服务器配置
	Port    string
	GinMode string

	// 数据库配置
	Database DatabaseConfig

	// JWT 配置
	JWT JWTConfig

	// CORS 配置
	CORS CORSConfig

	// 文件上传配置
	Upload UploadConfig
}

type DatabaseConfig struct {
	Type     string // sqlite 或 mysql
	Host     string
	Port     string
	Name     string
	User     string
	Password string
	Charset  string
	SQLitePath string
}

type JWTConfig struct {
	Secret      string
	ExpireHours int
}

type CORSConfig struct {
	Origins []string
}

type UploadConfig struct {
	Path        string
	MaxSizeMB   int
}

func Load() *Config {
	return &Config{
		Port:    getEnv("PORT", "8080"),
		GinMode: getEnv("GIN_MODE", "debug"),
		
		Database: DatabaseConfig{
			Type:       getEnv("DB_TYPE", "sqlite"),
			Host:       getEnv("DB_HOST", "localhost"),
			Port:       getEnv("DB_PORT", "3306"),
			Name:       getEnv("DB_NAME", "shop_order"),
			User:       getEnv("DB_USER", "root"),
			Password:   getEnv("DB_PASSWORD", ""),
			Charset:    getEnv("DB_CHARSET", "utf8mb4"),
			SQLitePath: getEnv("SQLITE_PATH", "./data/shop_order.db"),
		},
		
		JWT: JWTConfig{
			Secret:      getEnv("JWT_SECRET", "your-super-secret-jwt-key"),
			ExpireHours: getEnvInt("JWT_EXPIRE_HOURS", 24),
		},
		
		CORS: CORSConfig{
			Origins: strings.Split(getEnv("CORS_ORIGINS", "http://localhost:5173"), ","),
		},
		
		Upload: UploadConfig{
			Path:      getEnv("UPLOAD_PATH", "./uploads"),
			MaxSizeMB: getEnvInt("MAX_UPLOAD_SIZE", 10),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
